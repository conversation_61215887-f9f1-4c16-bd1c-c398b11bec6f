## ProtocolExpert 测试方案（核心功能：自动生成 TDS 参数校验函数）

### 1. 项目简介（约170字）
ProtocolExpert 以“数据侧样本抽取 + 模型侧代码生成”的方式，面向 TDS 协议调用场景构建智能安全校验能力。其一，基于 pyshark 对 PCAP/TDS 流量进行解析与聚合，自动沉淀各业务调用的参数样本库；其二，驱动大模型从“已知合法样本”出发生成 check_valid 函数，并在失败样本反馈与自动代码修复（Agent_DebugCode）闭环下迭代完善，最终得到可直接导入执行的 Python 校验函数，既保证对样本的 100% 通过率，又尽量具备合理的泛化检测能力。

### 2. 测试目标
- 功能完整性：覆盖“样本读取 → 代码生成 → 导入执行 → 反馈修正”的端到端闭环。
- 校验准确性：对训练样本通过率 100%；对常见非法/边界输入具备拦截效果。
- 泛化能力：在留出集和合成边界样本上保持稳定判定，不依赖硬编码样本值。
- 稳定性与鲁棒性：异常响应、语法错误与重复输出可被检测与恢复，流程不崩溃。
- 可重复性：在固定 stub/seed 环境下，多次执行得到一致结果与产物。

### 2.1 测试覆盖度评估结果
经对比项目实际代码，当前测试方案存在以下覆盖度问题：
- **遗漏关键路径**：未覆盖 shot_circle 递归调用路径（用于处理大数据集的分批重试）
- **参数组合不全**：缺少 tds_info 参数的详细测试（影响 prompt 模板选择）
- **边界条件不足**：get_adaptive_size 的边界逻辑、feedback_name 切换机制未充分测试
- **集成点遗漏**：与 Prompt 模板系统的集成测试不足

### 3. 测试策略与环境
- 测试层次：
  - 单元：Common、RepeatDetector、TDS、TKV_summarizer 的关键函数
  - 组件：Agent_GenerateTDSFunction 的 one_shot、shot_with_feedback 行为
  - 集成：替换 OpenAI 客户端为 stub，模拟多轮生成/反馈/修复
  - 端到端：从样本数据到产出校验函数，包含真实导入与执行
- 环境要求：Python 3.9+；依赖建议见 docs/analysis.md；如需真实联机，需可用的 OpenAI 兼容推理服务（或 vLLM 网关）。
- 可测试性改造（建议）：在测试中以子类/猴补方式覆盖 Agent.chat 或 Agent.run，以便注入确定性的“生成代码”。

### 4. 功能测试用例设计

以下用例均可在本地以“pytest/python 脚本”方式运行；若未部署 LLM，请使用 stub 覆盖 Agent.run/Agent_DebugCode.shot，保证可重复。

用例 A1：正常流程-纯数字样本，单轮即通过
- 前置条件：构造数据 data=[1, 2, 3, 10, 100]；以 stub 覆盖 Agent.run 返回简单范围检查函数（e.g. 0<=x<=1000）。
- 测试步骤：
  1) 实例化 Agent_GenerateTDSFunction（关闭真实网络或替换 run）。
  2) 调用 shot_all(data, dst_dir='debug', file_name='check_valid.py')。
  3) 通过 Common.import_function 动态导入 check_valid。
  4) 逐项校验 data，记录结果。
- 预期结果：shot_all 返回 True；对 data 全部返回 True；生成文件存在且可导入。
- 验证标准：通过率=100%，无异常日志。

用例 A2：正常流程-字符串样本（含长度/字符集规则）
- 前置条件：data=['AB123', 'XY999', 'CD001']；stub 返回规则“长度=5，前2位字母后3位数字”。
- 测试步骤：同 A1。
- 预期结果：A1 同；再对负例（'A1234','AB12X','ABCDE'）返回 False。
- 验证标准：训练集通过率=100%；负例误判率≤20%。

用例 A3：初版函数未覆盖全部样本，触发反馈迭代
- 前置条件：data=['A1','A2','B10']；第一次 run 产出仅允许'字母+单数字'；当 shot_with_feedback 触发 feedback 时，第二次 run 返回兼容'字母+1~2位数字'的函数。
- 测试步骤：执行 shot_all；确保触发一次反馈后通过。
- 预期结果：最终返回 True；生成函数对 'B10' 也返回 True。
- 验证标准：至少发生一次反馈；最终训练集通过率=100%。

用例 A4：生成代码包含语法错误，触发自动修复
- 前置条件：第一次 run 返回缺失冒号/括号的函数；以猴补将 Agent_DebugCode.shot 固定返回“已修复函数”。
- 测试步骤：执行 shot_all。
- 预期结果：流程不中断；最终函数可导入执行且通过样本。
- 验证标准：捕获到一次语法错误；修复后通过率=100%。

用例 A5：泛化能力-留出集与边界值
- 前置条件：训练 data 为 5~50 的整数；留出集 valid=[5,50,6,49], 边界/越界 invalid=[4,51,'a',5.5]；stub 生成“范围检查”函数。
- 测试步骤：训练后导入函数，对 valid/invalid 批量校验。
- 预期结果：valid 全 True；invalid 至少 3 项 False。
- 验证标准：留出集通过率=100%；非法拦截率≥75%。

用例 A6：大批量样本与分批策略（get_adaptive_size）
- 前置条件：构造上千条等长字符串或数值；stub 生成宽松规则以避免生成失败。
- 测试步骤：记录 batch_size 传入与内部计算，确保不会 OOM 或无限循环。
- 预期结果：shot_all 在有限轮数内返回；产出函数可用。
- 验证标准：总耗时可控（自定阈值），日志无重复检测警告刷屏。

用例 A7：异常-LLM 不可用/超时
- 前置条件：不提供可用 base_url，或让 Agent.run 抛异常。
- 测试步骤：执行 shot_all；捕获异常并重试。
- 预期结果：流程在最大重试后失败并给出明确错误；不留下半成品文件。
- 验证标准：异常信息可读；资源清理到位。

用例 A8：异常-生成非预期格式（未包含函数/函数名不符）
- 前置条件：run 返回非代码文本或缺少 check_valid。
- 测试步骤：执行 one_shot/shot_with_feedback。
- 预期结果：捕获 AttributeError/导入失败并触发反馈或失败返回。
- 验证标准：错误被记录；流程按设计退出或继续。

用例 A9：与 TKV_summarizer 集成-样本库生成最小闭环
- 前置条件：伪造一行 TDS 负载："execute arith..CS50_Same_City @k1='A',@k2='B'"。
- 测试步骤：直接调用 TKV_summarizer.run_pkg_execute_TDS(None,None,None,line)；执行 prost_preocess()；从 output/CS50_Same_City/ 读取样本文件并据此执行 A1/A2 流程。
- 预期结果：样本文件生成且去重；基于其成功产出校验函数。
- 验证标准：端到端最小闭环打通。

用例 A10：重复检测器与安全阈值
- 前置条件：构造包含重复段落的长文本；调用 RepeatDetector.update_and_check。
- 测试步骤：模拟流式追加，观察触发点。
- 预期结果：在预设重复阈值内返回 True，重置后恢复正常。
- 验证标准：检测灵敏且无误报（可通过多组数据评估）。

### 5. 数据准备与执行指引
- 构造样本：字符串请用单引号包裹以便 Common.read_txt_with_dtype 识别为 str；数值按行直接填入。
- 无 LLM 情况：
  - 子类化 Agent_GenerateTDSFunction 覆盖 run/ chat；或使用 monkeypatch 将 run 固定返回期望函数代码字符串。
  - 覆盖 Agent_DebugCode.shot 直接返回“修复后函数”。
- 真实联机（可选）：配置 base_url、model、api_key 并确保本地/远端 vLLM 网关可用；注意部署 pyshark 需要系统安装 tshark。

### 6. 自动化与度量
- 建议在 tests/ 下新增：
  - unit/test_common_repeat.py、unit/test_tds_utils.py
  - component/test_agent_generate_one_shot.py
  - e2e/test_validator_generation.py（含 A1~A6）
- 基本度量：
  - 训练样本通过率=100%
  - 留出集通过率≥95%
  - 非法样本拦截率≥70%
  - 端到端执行时长阈值（示例：≤120s，stub 环境除外）

### 7. 交付物与路径
- 本测试方案：docs/test_plan.md
- 参考分析与依赖：docs/analysis.md
- 测试新增脚本（建议）：按第6节所列在 tests/ 组织并配套 README 说明

