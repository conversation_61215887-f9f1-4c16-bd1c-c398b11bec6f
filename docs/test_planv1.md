## ProtocolExpert 测试方案（核心功能：自动生成 TDS 参数校验函数）

### 1. 项目简介（约170字）
ProtocolExpert 以"数据侧样本抽取 + 模型侧代码生成"的方式，面向 TDS 协议调用场景构建智能安全校验能力。其一，基于 pyshark 对 PCAP/TDS 流量进行解析与聚合，自动沉淀各业务调用的参数样本库；其二，驱动大模型从"已知合法样本"出发生成 check_valid 函数，并在失败样本反馈与自动代码修复（Agent_DebugCode）闭环下迭代完善，最终得到可直接导入执行的 Python 校验函数，既保证对样本的 100% 通过率，又尽量具备合理的泛化检测能力。

### 2. 测试目标
- 功能完整性：覆盖"样本读取 → 代码生成 → 导入执行 → 反馈修正"的端到端闭环。
- 校验准确性：对训练样本通过率 100%；对常见非法/边界输入具备拦截效果。
- 泛化能力：在留出集和合成边界样本上保持稳定判定，不依赖硬编码样本值。
- 稳定性与鲁棒性：异常响应、语法错误与重复输出可被检测与恢复，流程不崩溃。
- 可重复性：在固定 stub/seed 环境下，多次执行得到一致结果与产物。

### 2.1 测试覆盖度评估结果
经对比项目实际代码，当前测试方案存在以下覆盖度问题：
- **遗漏关键路径**：未覆盖 shot_circle 递归调用路径（用于处理大数据集的分批重试）
- **参数组合不全**：缺少 tds_info 参数的详细测试（影响 prompt 模板选择）
- **边界条件不足**：get_adaptive_size 的边界逻辑、feedback_name 切换机制未充分测试
- **集成点遗漏**：与 Prompt 模板系统的集成测试不足

### 2.2 过拟合风险评估（重要补充）
经深入分析，发现当前系统存在显著的过拟合风险，而测试方案对此覆盖严重不足：
- **系统设计倾向**：prompt 模板要求"确保每一行的都能检查通过"，推动过拟合
- **反馈机制问题**：只有 100% 通过率才认为成功，缺少泛化能力验证
- **样本偏差风险**：随机采样可能错过关键边界样本，导致生成函数缺乏代表性
- **安全检测价值**：缺少对生成函数实际安全防护能力的验证

### 3. 测试策略与环境
- 测试层次：
  - 单元：Common、RepeatDetector、TDS、TKV_summarizer 的关键函数
  - 组件：Agent_GenerateTDSFunction 的 one_shot、shot_with_feedback、shot_all 行为
  - 集成：替换 OpenAI 客户端为 stub，模拟多轮生成/反馈/修复
  - 端到端：从样本数据到产出校验函数，包含真实导入与执行
- 环境要求：Python 3.9+；依赖建议见 docs/analysis.md；如需真实联机，需可用的 OpenAI 兼容推理服务（或 vLLM 网关）。
- 可测试性改造（关键）：
  - 覆盖 Agent.run 方法返回预设代码字符串（避免真实 LLM 调用）
  - 覆盖 Agent_DebugCode.shot 返回修复后代码
  - 使用 pytest.monkeypatch 或继承方式实现 stub
  - 提供多种预设响应模拟不同生成质量（正确、语法错误、逻辑错误等）

### 4. 功能测试用例设计（修订版）

以下用例均可在本地以"pytest/python 脚本"方式运行；若未部署 LLM，请使用 stub 覆盖 Agent.run/Agent_DebugCode.shot，保证可重复。

#### 4.1 基础功能测试

用例 A1：正常流程-纯数字样本，单轮即通过
- 前置条件：构造数据 data=[1, 2, 3, 10, 100]；以 stub 覆盖 Agent.run 返回简单范围检查函数：
  ```python
  def check_valid(data):
      return isinstance(data, int) and 0 <= data <= 1000
  ```
- 测试步骤：
  1) 实例化 Agent_GenerateTDSFunction（关闭真实网络或替换 run）。
  2) 调用 shot_all(data, dst_dir='debug', file_name='check_valid.py')。
  3) 通过 Common.import_function 动态导入 check_valid。
  4) 逐项校验 data，记录结果。
- 预期结果：shot_all 返回 True；对 data 全部返回 True；生成文件存在且可导入。
- 验证标准：通过率=100%，无异常日志。

用例 A2：正常流程-字符串样本（含长度/字符集规则）
- 前置条件：data=['AB123', 'XY999', 'CD001']；stub 返回规则函数：
  ```python
  def check_valid(data):
      return len(data) == 5 and data[:2].isalpha() and data[2:].isdigit()
  ```
- 测试步骤：同 A1。
- 预期结果：A1 同；再对负例（'A1234','AB12X','ABCDE'）返回 False。
- 验证标准：训练集通过率=100%；负例拦截率≥75%（基准：4个负例中至少3个被拦截）。

用例 A3：初版函数未覆盖全部样本，触发反馈迭代
- 前置条件：data=['A1','A2','B10']；第一次 run 产出代码：
  ```python
  def check_valid(data):
      return len(data) == 2 and data[0].isalpha() and data[1].isdigit()
  ```
  当 shot_with_feedback 触发 feedback 时，第二次 run 返回修正函数：
  ```python
  def check_valid(data):
      return len(data) >= 2 and data[0].isalpha() and data[1:].isdigit()
  ```
- 测试步骤：执行 shot_all；监控反馈触发；验证最终函数对所有样本的校验结果。
- 预期结果：最终返回 True；生成函数对 'B10' 也返回 True；反馈机制被正确触发。
- 验证标准：至少发生一次反馈；最终训练集通过率=100%；反馈前 'B10' 返回 False，反馈后返回 True。

用例 A4：生成代码包含语法错误，触发自动修复
- 前置条件：第一次 run 返回语法错误代码（缺失冒号）：
  ```python
  def check_valid(data)  # 缺失冒号
      return isinstance(data, int) and 0 <= data <= 100
  ```
  Agent_DebugCode.shot 返回修复后代码：
  ```python
  def check_valid(data):
      return isinstance(data, int) and 0 <= data <= 100
  ```
- 测试步骤：执行 shot_all；监控语法错误捕获和修复过程；验证修复后函数的可用性。
- 预期结果：流程不中断；最终函数可导入执行且通过样本；语法错误被正确识别和修复。
- 验证标准：捕获到一次 SyntaxError 或 exec 异常；Agent_DebugCode.shot 被调用一次；修复后通过率=100%。

#### 4.2 边界条件与异常测试

用例 A5：泛化能力-留出集与边界值
- 前置条件：训练 data 为 [5,10,15,20,25,30,35,40,45,50]；留出集 valid=[5,50,6,49]；边界/越界 invalid=[4,51,'a',5.5]；stub 生成范围检查函数：
  ```python
  def check_valid(data):
      return isinstance(data, int) and 5 <= data <= 50
  ```
- 测试步骤：训练后导入函数，对 valid/invalid 批量校验。
- 预期结果：valid 全 True；invalid 至少 3 项 False。
- 验证标准：留出集通过率=100%；非法拦截率≥75%（基准：4个非法样本中至少3个被拦截）。

用例 A6：大批量样本与分批策略（get_adaptive_size）
- 前置条件：构造 1000 条等长字符串 data=['TEST{:03d}'.format(i) for i in range(1000)]；stub 生成宽松规则：
  ```python
  def check_valid(data):
      return len(data) == 7 and data.startswith('TEST') and data[4:].isdigit()
  ```
- 测试步骤：记录 batch_size 传入与内部计算；监控内存使用；确保不会 OOM 或无限循环。
- 预期结果：shot_all 在有限轮数内返回；产出函数可用；batch_size 被正确计算。
- 验证标准：总耗时≤60s（stub 环境）；内存使用稳定；日志无重复检测警告刷屏。

用例 A7：异常-LLM 不可用/超时
- 前置条件：不提供可用 base_url，或让 Agent.run 抛 ConnectionError 异常。
- 测试步骤：执行 shot_all；捕获异常并重试。
- 预期结果：流程在最大重试后失败并给出明确错误；不留下半成品文件。
- 验证标准：异常信息可读；资源清理到位；临时文件被删除。

用例 A8：异常-生成非预期格式（未包含函数/函数名不符）
- 前置条件：run 返回非代码文本："这是一个关于数据校验的说明文档..."或缺少 check_valid 的代码：
  ```python
  def validate_data(data):  # 函数名错误
      return True
  ```
- 测试步骤：执行 one_shot/shot_with_feedback。
- 预期结果：捕获 AttributeError/导入失败并触发反馈或失败返回。
- 验证标准：错误被记录；流程按设计退出或继续；不产生无效文件。

#### 4.3 新增关键路径测试

用例 B1：shot_circle 递归调用测试
- 前置条件：构造 1000+ 样本，设置小 batch_size=50；第一轮生成函数只通过前 800 个样本。
- 测试步骤：执行 shot_all；监控 shot_circle 递归调用次数；验证 no_pass_data 的收敛过程。
- 预期结果：递归调用 2-3 次后收敛；最终所有样本通过；不超过 max_try 限制。
- 验证标准：self.cnt 递增记录；no_pass_data 逐步减少；最终返回 True。

用例 B2：get_adaptive_size 边界测试
- 前置条件：测试多种场景：
  - batch_size=0 且数据长度一致：data=['ABC', 'DEF', 'GHI']
  - batch_size=0 且数据长度不一致：data=['A', 'BB', 'CCC']
  - 超过 max_char_size 限制：data=['X'*100 for _ in range(200)]
- 测试步骤：分别调用 get_adaptive_size，记录返回值。
- 预期结果：
  - 长度一致时：返回 min(max_char_size//3, 3) = 3
  - 长度不一致时：返回 len(data) = 3
  - 超限时：返回合理的批次大小
- 验证标准：返回值符合预期逻辑；不抛异常。

用例 B3：TDS 格式样例集成测试
- 前置条件：构造有效的 tds_info 参数：
  ```python
  tds_info = {'preifx': 'execute arith', 'key': '@param1', 'idx': None}
  ```
  验证 content_new 模板被正确使用。
- 测试步骤：调用 shot_all 时传入 tds_info；监控 prompt 模板选择。
- 预期结果：使用 content_new 而非 content 模板；生成的函数考虑 TDS 格式上下文。
- 验证标准：run 方法被调用时 k_str='content_new'；生成函数质量提升。

用例 B4：feedback_name 切换机制测试
- 前置条件：设置 self.feedback_name = 'feedback1'；构造部分失败样本。
- 测试步骤：触发反馈时验证 build_json_data1 被调用而非 build_json_data。
- 预期结果：反馈数据格式为 {"sample1": "passed", "sample2": "unpassed"}。
- 验证标准：JSON 格式符合 feedback1 预期；包含 passed/unpassed 标记。

#### 4.4 性能与资源测试

用例 C1：性能测试-大数据集处理
- 前置条件：构造 5000 个样本；监控内存和时间消耗。
- 测试步骤：执行完整流程；记录各阶段耗时。
- 预期结果：单个样本处理时间 < 100ms；总处理时间 < 300s。
- 验证标准：性能指标在可接受范围内；无内存泄漏。

用例 C2：资源清理测试
- 前置条件：模拟异常中断（KeyboardInterrupt）。
- 测试步骤：在不同阶段中断流程；检查临时文件清理。
- 预期结果：临时文件被正确清理；无资源泄漏。
- 验证标准：测试目录干净；无残留 .py 文件。

用例 C3：边界数据集测试
- 前置条件：测试特殊数据集：
  - 空数据集：data=[]
  - 单元素数据集：data=[42]
  - 混合类型：data=[1, 'A', 3.14]（应被跳过）
- 测试步骤：分别执行 shot_all。
- 预期结果：空集和单元素正确处理；混合类型被合理拒绝。
- 验证标准：不抛未处理异常；返回合理的错误或跳过信息。

#### 4.5 过拟合风险检测测试（新增关键测试）

用例 E1：硬编码检测测试
- 前置条件：训练样本 data=['USER001', 'USER002', 'USER003']；stub 返回可能的硬编码函数：
  ```python
  def check_valid(data):
      return data in ['USER001', 'USER002', 'USER003']  # 硬编码样本值
  ```
- 测试步骤：
  1) 生成校验函数并分析代码内容
  2) 测试同类型新样本：['USER004', 'USER005', 'ADMIN001']
  3) 检测函数是否基于模式而非枚举
- 预期结果：同类型新样本中 USER004、USER005 应通过；ADMIN001 可能被拒绝（格式不同）。
- 验证标准：代码中不包含训练样本的字面值；同类型新样本通过率 ≥ 80%；函数逻辑基于模式识别。

用例 E2：过度拟合检测测试
- 前置条件：构造具有微妙偏差的训练样本：
  ```python
  data = ['2024-01-15', '2024-02-20', '2024-03-25']  # 都是月中日期
  ```
  stub 返回过度拟合的函数：
  ```python
  def check_valid(data):
      return len(data) == 10 and data[8:10] in ['15', '20', '25']  # 过度依赖特定日期
  ```
- 测试步骤：测试边界日期：['2024-01-01', '2024-01-31', '2024-02-29']
- 预期结果：合法日期应该通过，不应该因为"不是月中"而被拒绝。
- 验证标准：月初月末日期通过率 ≥ 90%；函数逻辑基于日期合法性而非特定日期模式。

用例 E3：规则失真检测测试
- 前置条件：训练样本包含明显异常值：
  ```python
  data = ['NORMAL001', 'NORMAL002', 'HACK<script>alert(1)</script>']
  ```
  测试生成函数是否为了通过所有样本而过度放宽规则。
- 测试步骤：
  1) 生成校验函数
  2) 测试是否能正确拒绝恶意输入：['<script>evil</script>', 'DROP TABLE users']
- 预期结果：函数应该拒绝包含脚本标签和SQL注入的输入。
- 验证标准：恶意输入拦截率 = 100%；正常输入通过率 ≥ 95%。

用例 E4：样本偏差检测测试
- 前置条件：训练样本只包含特定格式偏差：
  ```python
  data = ['ABC123', 'DEF456', 'GHI789']  # 只有大写字母
  ```
- 测试步骤：测试格式变体：['abc123', 'Def456', 'gHi789']（小写和混合大小写）
- 预期结果：如果业务允许大小写混合，应有合理通过率。
- 验证标准：格式变体通过率 30-80%（取决于业务需求）；函数应基于语义而非严格格式匹配。

用例 E5：泛化能力增强测试
- 前置条件：训练样本为用户ID格式：data=['USER001', 'USER002', 'USER003']
- 测试步骤：测试跨域泛化：
  ```python
  similar = ['ADMIN001', 'GUEST001', 'TEMP001']    # 相似格式
  different = ['U001', 'USER-001', 'user001']      # 不同格式
  threats = ["'; DROP TABLE users; --", "<script>"] # 安全威胁
  ```
- 预期结果：相似格式有适当通过率；不同格式多数被拒绝；安全威胁全部被拒绝。
- 验证标准：相似格式通过率 30-70%；不同格式拒绝率 ≥ 80%；安全威胁拦截率 = 100%。

#### 4.6 集成测试

用例 D1：与 TKV_summarizer 集成-样本库生成最小闭环
- 前置条件：伪造一行 TDS 负载："execute arith..CS50_Same_City @k1='A',@k2='B'"。
- 测试步骤：直接调用 TKV_summarizer.run_pkg_execute_TDS(None,None,None,line)；执行 prost_preocess()；从 output/CS50_Same_City/ 读取样本文件并据此执行 A1/A2 流程。
- 预期结果：样本文件生成且去重；基于其成功产出校验函数。
- 验证标准：端到端最小闭环打通；文件格式正确。

用例 D2：重复检测器与安全阈值
- 前置条件：构造包含重复段落的长文本；调用 RepeatDetector.update_and_check。
- 测试步骤：模拟流式追加，观察触发点。
- 预期结果：在预设重复阈值内返回 True，重置后恢复正常。
- 验证标准：检测灵敏且无误报（可通过多组数据评估）。

### 5. 数据准备与执行指引
- 构造样本：字符串请用单引号包裹以便 Common.read_txt_with_dtype 识别为 str；数值按行直接填入。
- 标准化测试数据构造：
  ```python
  def create_test_data(data_type, count, **kwargs):
      if data_type == 'string':
          return [f"'TEST{i:03d}'" for i in range(count)]
      elif data_type == 'integer':
          return list(range(kwargs.get('start', 0), count))
      elif data_type == 'mixed':
          return [1, 'A', 3.14, True]  # 用于测试类型检查
  ```
- 无 LLM 情况：
  - 子类化 Agent_GenerateTDSFunction 覆盖 run/ chat；或使用 monkeypatch 将 run 固定返回期望函数代码字符串。
  - 覆盖 Agent_DebugCode.shot 直接返回"修复后函数"。
- 真实联机（可选）：配置 base_url、model、api_key 并确保本地/远端 vLLM 网关可用；注意部署 pyshark 需要系统安装 tshark。

### 6. 自动化与度量
- 建议在 tests/ 下新增：
  - unit/test_common_repeat.py、unit/test_tds_utils.py、unit/test_get_adaptive_size.py
  - component/test_agent_generate_one_shot.py、component/test_shot_circle.py
  - integration/test_agent_prompt_integration.py、integration/test_tkv_agent_pipeline.py
  - e2e/test_validator_generation.py（含 A1~A6、B1~B4、C1~C3）
- 量化度量标准：
  - 训练样本通过率=100%（必须）
  - 留出集通过率≥95%（基准：标准留出集）
  - 非法样本拦截率≥75%（基准：类型错误、长度越界、格式错误等标准非法样本集）
  - **泛化能力指标（新增）**：
    - 同类型新样本通过率：70-90%
    - 跨类型样本拒绝率：≥80%
    - 安全威胁拦截率：=100%
    - 硬编码检测：代码中不包含训练样本字面值
  - 端到端执行时长阈值：≤120s（真实 LLM），≤30s（stub 环境）
  - 内存使用峰值：≤500MB（1000 样本测试）

### 7. 交付物与路径
- 本测试方案：docs/test_plan.md
- 参考分析与依赖：docs/analysis.md
- 审查报告：docs/test_plan_review.md
- **过拟合风险分析**：docs/overfitting_analysis.md（详细分析过拟合问题与改进建议）
- 测试新增脚本（建议）：按第6节所列在 tests/ 组织并配套 README 说明

### 8. 过拟合风险缓解建议
基于过拟合风险分析，建议在实施测试时重点关注：
- **优先实施 E1-E5 过拟合检测用例**，确保生成函数具有实际价值
- **增强泛化能力验证**，避免生成硬编码或过度严格的规则
- **引入安全威胁测试**，验证函数的实际防护能力
- **改进 prompt 模板**，在保证样本通过的同时强调泛化能力
- **完善反馈机制**，不仅考虑训练样本通过率，还要验证泛化表现
