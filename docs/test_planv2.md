## ProtocolExpert 测试方案（核心功能：自动生成 TDS 参数校验函数）

### 1. 项目简介（约170字）
ProtocolExpert 以"数据侧样本抽取 + 模型侧代码生成"的方式，面向 TDS 协议调用场景构建智能安全校验能力。其一，基于 pyshark 对 PCAP/TDS 流量进行解析与聚合，自动沉淀各业务调用的参数样本库；其二，驱动大模型从"已知合法样本"出发生成 check_valid 函数，并在失败样本反馈与自动代码修复（Agent_DebugCode）闭环下迭代完善，最终得到可直接导入执行的 Python 校验函数，既保证对样本的 100% 通过率，又尽量具备合理的泛化检测能力。

### 2. 测试目标
- 功能完整性：覆盖"样本读取 → 代码生成 → 导入执行 → 反馈修正"的端到端闭环。
- 校验准确性：对训练样本通过率 100%；对常见非法/边界输入具备拦截效果。
- 泛化能力：在留出集和合成边界样本上保持稳定判定，不依赖硬编码样本值。
- 稳定性与鲁棒性：异常响应、语法错误与重复输出可被检测与恢复，流程不崩溃。

### 2.1 测试覆盖度评估结果
经对比项目实际代码，当前测试方案存在以下覆盖度问题：
- **遗漏关键路径**：未覆盖 shot_circle 递归调用路径（用于处理大数据集的分批重试）
- **参数组合不全**：缺少 tds_info 参数的详细测试（影响 prompt 模板选择）
- **边界条件不足**：get_adaptive_size 的边界逻辑、feedback_name 切换机制未充分测试
- **集成点遗漏**：与 Prompt 模板系统的集成测试不足

### 2.2 过拟合风险评估（重要补充）
经深入分析，发现当前系统存在显著的过拟合风险，而测试方案对此覆盖严重不足：
- **系统设计倾向**：prompt 模板要求"确保每一行的都能检查通过"，推动过拟合
- **反馈机制问题**：只有 100% 通过率才认为成功，缺少泛化能力验证
- **样本偏差风险**：随机采样可能错过关键边界样本，导致生成函数缺乏代表性
- **安全检测价值**：缺少对生成函数实际安全防护能力的验证

### 3. 测试策略与环境
- 测试层次：
  - 单元：Common、RepeatDetector、TDS、TKV_summarizer 的关键函数
  - 组件：Agent_GenerateTDSFunction 的 one_shot、shot_with_feedback、shot_all 行为
  - 集成：替换 OpenAI 客户端为 stub，模拟多轮生成/反馈/修复
  - 端到端：从样本数据到产出校验函数，包含真实导入与执行
- 环境要求：Python 3.9+；依赖建议见 docs/analysis.md；如需真实联机，需可用的 OpenAI 兼容推理服务（或 vLLM 网关）。
- 可测试性改造（关键）：
  - 覆盖 Agent.run 方法返回预设代码字符串（避免真实 LLM 调用）
  - 覆盖 Agent_DebugCode.shot 返回修复后代码
  - 使用 pytest.monkeypatch 或继承方式实现 stub
  - 提供多种预设响应模拟不同生成质量（正确、语法错误、逻辑错误等）

### 4. 功能测试用例设计（基于现有脚本）

以下用例基于项目现有的 `tests/main.py` 脚本和数据文件结构进行黑盒测试，无需手动编写验证代码。

#### 4.1 基础功能测试

用例 A1：正常流程-使用现有样本文件测试单个函数生成
- 前置条件：
  - 确认存在样本文件：`output/B_get_admin_auth/@admin_code.txt`
  - 清理之前的测试结果：删除 `debug/` 目录下的 `test.py` 文件
- 测试步骤：
  1) 进入 `tests/` 目录：`cd tests`
  2) 修改 `main.py` 第18行，取消注释：`test_GenerateTDSFunction()`
  3) 注释掉第53行：`# run_dir('../guangtie_params')`
  4) 执行脚本：`python main.py`
  5) 检查生成的文件：`ls ../debug/test.py`
- 预期结果：
  - 脚本执行无错误退出
  - 生成文件 `debug/test.py` 包含 `check_valid` 函数
  - 控制台输出 "All passed" 表示样本验证成功
- 验证标准：
  - 检查文件存在：`test -f ../debug/test.py && echo "文件生成成功"`
  - 检查函数内容：`grep -q "def check_valid" ../debug/test.py && echo "函数生成成功"`

用例 A2：正常流程-字符串样本测试（日期格式）
- 前置条件：
  - 确认存在样本文件：`output/CS50_Same_City/@train_date.txt`（包含日期格式字符串）
  - 清理之前的测试结果：删除该目录下的 `.py` 和 `.passed` 文件
- 测试步骤：
  1) 进入 `tests/` 目录：`cd tests`
  2) 修改 `main.py` 创建单文件测试函数：
     ```python
     def test_single_file(file_path):
         agent = Agent_GenerateTDSFunction()
         tds_info = TDS.get_info_form_path(file_path, '../output')
         data = Common.read_txt_with_dtype(file_path)
         path, filename = os.path.split(file_path)
         passed = agent.shot_all(data, dst_dir=path, file_name=filename.replace('.txt','.py'), tds_info=tds_info)
         return passed
     ```
  3) 在 `main()` 中调用：`test_single_file('../output/CS50_Same_City/@train_date.txt')`
  4) 执行脚本：`python main.py`
  5) 检查生成的文件：`ls ../output/CS50_Same_City/@train_date.py`
- 预期结果：
  - 函数返回 True 表示生成成功
  - 生成文件 `output/CS50_Same_City/@train_date.py` 包含日期格式校验逻辑
  - 生成文件 `output/CS50_Same_City/@train_date.passed` 表示处理成功
- 验证标准：
  - 检查文件存在：`test -f ../output/CS50_Same_City/@train_date.py && echo "校验函数生成成功"`
  - 检查标记文件：`test -f ../output/CS50_Same_City/@train_date.passed && echo "处理标记生成成功"`

用例 A3：批量目录处理测试-使用现有 run_dir 函数
- 前置条件：
  - 确认存在目录：`output/` 包含多个子目录和样本文件
  - 清理之前的测试结果：删除 `output/` 下所有 `.py` 和 `.passed` 文件
- 测试步骤：
  1) 进入 `tests/` 目录：`cd tests`
  2) 修改 `main.py` 第53行，改为测试较小的目录：`run_dir('../output/CS50_Same_City')`
  3) 注释掉第48行：`# test_GenerateTDSFunction()`
  4) 执行脚本：`python main.py`
  5) 观察控制台输出的处理进度和结果
  6) 检查生成的文件：`find ../output/CS50_Same_City -name "*.py" -o -name "*.passed"`
- 预期结果：
  - 脚本处理目录下所有 `.txt` 文件
  - 跳过包含逗号的字段（控制台显示 continue）
  - 为每个成功处理的文件生成对应的 `.py` 和 `.passed` 文件
  - 控制台显示 "save ---------------" 表示成功保存
- 验证标准：
  - 检查生成文件数量：`ls ../output/CS50_Same_City/*.py | wc -l` 应 > 0
  - 检查标记文件：`ls ../output/CS50_Same_City/*.passed | wc -l` 应与 `.py` 文件数量相等

用例 A4：验证已生成函数的正确性-使用 Common.check_pass_all
- 前置条件：
  - 已完成用例 A3，确保 `output/CS50_Same_City/` 下有生成的 `.py` 和 `.passed` 文件
- 测试步骤：
  1) 进入 `tests/` 目录：`cd tests`
  2) 修改 `main.py` 第55行，取消注释：`Common.check_pass_all('../output/CS50_Same_City')`
  3) 注释掉其他测试函数调用
  4) 执行脚本：`python main.py`
  5) 观察控制台输出的验证结果
- 预期结果：
  - 脚本遍历所有已生成的 `.py` 文件
  - 对每个文件重新加载并验证原始样本
  - 控制台显示 "✅" 表示验证通过，"❌" 表示验证失败
  - 所有生成的函数都应该通过验证
- 验证标准：
  - 控制台输出全部为 "✅" 符号
  - 无 "❌" 符号出现
  - 脚本正常结束，无异常退出

#### 4.2 边界条件与异常测试

用例 A5：大规模目录处理测试-使用完整 guangtie_params 目录
- 前置条件：
  - 确认存在目录：`guangtie_params/` 包含大量子目录和样本文件
  - 清理之前的测试结果：删除 `guangtie_params/` 下所有 `.py` 和 `.passed` 文件
- 测试步骤：
  1) 进入 `tests/` 目录：`cd tests`
  2) 修改 `main.py` 第53行，恢复原始设置：`run_dir('../guangtie_params')`
  3) 注释掉其他测试函数调用
  4) 执行脚本：`python main.py`（注意：这可能需要较长时间）
  5) 观察处理进度和跳过的文件
  6) 统计处理结果：`find ../guangtie_params -name "*.passed" | wc -l`
- 预期结果：
  - 脚本处理大量文件，显示进度条
  - 跳过包含逗号的字段和已处理的文件
  - 成功处理的文件生成对应的 `.py` 和 `.passed` 文件
  - 部分文件可能因为数据格式问题被跳过
- 验证标准：
  - 成功处理文件数 > 0：`find ../guangtie_params -name "*.passed" | wc -l`
  - 生成的 `.py` 文件数量与 `.passed` 文件数量相等
  - 脚本正常结束，无崩溃

用例 A6：复杂数据类型测试-多参数样本文件
- 前置条件：
  - 选择包含复杂数据的样本文件：`output/DS60_create_seat_id_bsr/@id_nos.txt`（身份证号格式）
  - 清理之前的测试结果
- 测试步骤：
  1) 进入 `tests/` 目录：`cd tests`
  2) 修改 `main.py` 添加复杂数据测试函数：
     ```python
     def test_complex_data():
         agent = Agent_GenerateTDSFunction()
         file_path = '../output/DS60_create_seat_id_bsr/@id_nos.txt'
         tds_info = TDS.get_info_form_path(file_path, '../output')
         data = Common.read_txt_with_dtype(file_path)
         print(f"样本数量: {len(data)}")
         print(f"样本示例: {data[:3] if len(data) >= 3 else data}")
         path, filename = os.path.split(file_path)
         passed = agent.shot_all(data, dst_dir=path, file_name=filename.replace('.txt','.py'), tds_info=tds_info)
         print(f"处理结果: {passed}")
         return passed
     ```
  3) 在 `main()` 中调用：`test_complex_data()`
  4) 执行脚本：`python main.py`
- 预期结果：
  - 显示样本数量和示例内容
  - 成功生成针对身份证号格式的校验函数
  - 函数能够识别身份证号的基本格式规则
- 验证标准：
  - 函数返回 True
  - 生成的 `.py` 文件包含合理的身份证校验逻辑
  - 控制台输出显示处理成功

用例 A7：异常-LLM 不可用/超时
- 前置条件：不提供可用 base_url，或让 Agent.run 抛 ConnectionError 异常。
- 测试步骤：执行 shot_all；捕获异常并重试。
- 预期结果：流程在最大重试后失败并给出明确错误；不留下半成品文件。
- 验证标准：异常信息可读；资源清理到位；临时文件被删除。

用例 A8：异常-生成非预期格式（未包含函数/函数名不符）
- 前置条件：run 返回非代码文本："这是一个关于数据校验的说明文档..."或缺少 check_valid 的代码：
  ```python
  def validate_data(data):  # 函数名错误
      return True
  ```
- 测试步骤：执行 one_shot/shot_with_feedback。
- 预期结果：捕获 AttributeError/导入失败并触发反馈或失败返回。
- 验证标准：错误被记录；流程按设计退出或继续；不产生无效文件。

#### 4.3 新增关键路径测试

用例 B1：shot_circle 递归调用测试
- 前置条件：构造 1000+ 样本，设置小 batch_size=50；第一轮生成函数只通过前 800 个样本。
- 测试步骤：执行 shot_all；监控 shot_circle 递归调用次数；验证 no_pass_data 的收敛过程。
- 预期结果：递归调用 2-3 次后收敛；最终所有样本通过；不超过 max_try 限制。
- 验证标准：self.cnt 递增记录；no_pass_data 逐步减少；最终返回 True。

用例 B2：get_adaptive_size 边界测试
- 前置条件：测试多种场景：
  - batch_size=0 且数据长度一致：data=['ABC', 'DEF', 'GHI']
  - batch_size=0 且数据长度不一致：data=['A', 'BB', 'CCC']
  - 超过 max_char_size 限制：data=['X'*100 for _ in range(200)]
- 测试步骤：分别调用 get_adaptive_size，记录返回值。
- 预期结果：
  - 长度一致时：返回 min(max_char_size//3, 3) = 3
  - 长度不一致时：返回 len(data) = 3
  - 超限时：返回合理的批次大小
- 验证标准：返回值符合预期逻辑；不抛异常。

用例 B3：TDS 格式样例集成测试
- 前置条件：构造有效的 tds_info 参数：
  ```python
  tds_info = {'preifx': 'execute arith', 'key': '@param1', 'idx': None}
  ```
  验证 content_new 模板被正确使用。
- 测试步骤：调用 shot_all 时传入 tds_info；监控 prompt 模板选择。
- 预期结果：使用 content_new 而非 content 模板；生成的函数考虑 TDS 格式上下文。
- 验证标准：run 方法被调用时 k_str='content_new'；生成函数质量提升。

用例 B4：feedback_name 切换机制测试
- 前置条件：设置 self.feedback_name = 'feedback1'；构造部分失败样本。
- 测试步骤：触发反馈时验证 build_json_data1 被调用而非 build_json_data。
- 预期结果：反馈数据格式为 {"sample1": "passed", "sample2": "unpassed"}。
- 验证标准：JSON 格式符合 feedback1 预期；包含 passed/unpassed 标记。

#### 4.4 性能与资源测试

用例 C1：性能测试-大数据集处理
- 前置条件：构造 5000 个样本；监控内存和时间消耗。
- 测试步骤：执行完整流程；记录各阶段耗时。
- 预期结果：单个样本处理时间 < 100ms；总处理时间 < 300s。
- 验证标准：性能指标在可接受范围内；无内存泄漏。

用例 C2：资源清理测试
- 前置条件：模拟异常中断（KeyboardInterrupt）。
- 测试步骤：在不同阶段中断流程；检查临时文件清理。
- 预期结果：临时文件被正确清理；无资源泄漏。
- 验证标准：测试目录干净；无残留 .py 文件。

用例 C3：边界数据集测试
- 前置条件：测试特殊数据集：
  - 空数据集：data=[]
  - 单元素数据集：data=[42]
  - 混合类型：data=[1, 'A', 3.14]（应被跳过）
- 测试步骤：分别执行 shot_all。
- 预期结果：空集和单元素正确处理；混合类型被合理拒绝。
- 验证标准：不抛未处理异常；返回合理的错误或跳过信息。

#### 4.5 过拟合风险检测测试（基于现有脚本）

用例 E1：硬编码检测测试-分析生成的校验函数代码
- 前置条件：
  - 已完成用例 A1-A6，确保有多个生成的 `.py` 文件
  - 选择样本数量较少的文件进行分析，如：`output/CS50_Same_City/@train_date.py`
- 测试步骤：
  1) 检查生成的函数代码内容：`cat ../output/CS50_Same_City/@train_date.py`
  2) 分析函数是否包含硬编码的样本值
  3) 创建测试脚本验证泛化能力：
     ```python
     def test_generalization():
         import sys
         sys.path.append('../output/CS50_Same_City')
         from train_date import check_valid

         # 原始样本
         original = ['20210407', '20210408', '20210417']
         # 同类型新样本
         similar = ['20210409', '20210410', '20220101']
         # 不同格式样本
         different = ['2021-04-07', '21040', 'invalid']

         print("原始样本通过率:", sum(check_valid(x) for x in original) / len(original))
         print("同类型样本通过率:", sum(check_valid(x) for x in similar) / len(similar))
         print("不同格式拒绝率:", sum(not check_valid(x) for x in different) / len(different))
     ```
  4) 执行泛化测试：`python -c "exec(open('test_generalization.py').read())"`
- 预期结果：
  - 生成的函数基于日期格式模式而非硬编码样本值
  - 同类型新样本通过率 ≥ 80%
  - 不同格式样本拒绝率 ≥ 80%
- 验证标准：
  - 代码中不包含具体的日期字符串（如 '20210407'）
  - 包含日期格式检查逻辑（如长度、数字检查等）
  - 泛化测试结果符合预期阈值

用例 E2：过度拟合检测测试
- 前置条件：构造具有微妙偏差的训练样本：
  ```python
  data = ['2024-01-15', '2024-02-20', '2024-03-25']  # 都是月中日期
  ```
  stub 返回过度拟合的函数：
  ```python
  def check_valid(data):
      return len(data) == 10 and data[8:10] in ['15', '20', '25']  # 过度依赖特定日期
  ```
- 测试步骤：测试边界日期：['2024-01-01', '2024-01-31', '2024-02-29']
- 预期结果：合法日期应该通过，不应该因为"不是月中"而被拒绝。
- 验证标准：月初月末日期通过率 ≥ 90%；函数逻辑基于日期合法性而非特定日期模式。

用例 E3：规则失真检测测试
- 前置条件：训练样本包含明显异常值：
  ```python
  data = ['NORMAL001', 'NORMAL002', 'HACK<script>alert(1)</script>']
  ```
  测试生成函数是否为了通过所有样本而过度放宽规则。
- 测试步骤：
  1) 生成校验函数
  2) 测试是否能正确拒绝恶意输入：['<script>evil</script>', 'DROP TABLE users']
- 预期结果：函数应该拒绝包含脚本标签和SQL注入的输入。
- 验证标准：恶意输入拦截率 = 100%；正常输入通过率 ≥ 95%。

用例 E4：样本偏差检测测试
- 前置条件：训练样本只包含特定格式偏差：
  ```python
  data = ['ABC123', 'DEF456', 'GHI789']  # 只有大写字母
  ```
- 测试步骤：测试格式变体：['abc123', 'Def456', 'gHi789']（小写和混合大小写）
- 预期结果：如果业务允许大小写混合，应有合理通过率。
- 验证标准：格式变体通过率 30-80%（取决于业务需求）；函数应基于语义而非严格格式匹配。

用例 E5：泛化能力增强测试
- 前置条件：训练样本为用户ID格式：data=['USER001', 'USER002', 'USER003']
- 测试步骤：测试跨域泛化：
  ```python
  similar = ['ADMIN001', 'GUEST001', 'TEMP001']    # 相似格式
  different = ['U001', 'USER-001', 'user001']      # 不同格式
  threats = ["'; DROP TABLE users; --", "<script>"] # 安全威胁
  ```
- 预期结果：相似格式有适当通过率；不同格式多数被拒绝；安全威胁全部被拒绝。
- 验证标准：相似格式通过率 30-70%；不同格式拒绝率 ≥ 80%；安全威胁拦截率 = 100%。

#### 4.6 集成测试

用例 D1：与 TKV_summarizer 集成-样本库生成最小闭环
- 前置条件：伪造一行 TDS 负载："execute arith..CS50_Same_City @k1='A',@k2='B'"。
- 测试步骤：直接调用 TKV_summarizer.run_pkg_execute_TDS(None,None,None,line)；执行 prost_preocess()；从 output/CS50_Same_City/ 读取样本文件并据此执行 A1/A2 流程。
- 预期结果：样本文件生成且去重；基于其成功产出校验函数。
- 验证标准：端到端最小闭环打通；文件格式正确。

用例 D2：重复检测器与安全阈值
- 前置条件：构造包含重复段落的长文本；调用 RepeatDetector.update_and_check。
- 测试步骤：模拟流式追加，观察触发点。
- 预期结果：在预设重复阈值内返回 True，重置后恢复正常。
- 验证标准：检测灵敏且无误报（可通过多组数据评估）。

### 5. 数据准备与执行指引（基于现有脚本）

#### 5.1 测试环境准备
- **LLM 服务配置**：确保 Agent 类能够访问 OpenAI 兼容的 API 服务
  - 检查 `src/Agent.py` 中的 base_url、model、api_key 配置
  - 如使用本地 vLLM，确保服务正常运行
- **数据文件检查**：确认项目现有数据文件完整性
  - 检查 `output/` 目录下的样本文件格式正确
  - 检查 `guangtie_params/` 目录结构完整
- **依赖环境**：确保所有 Python 依赖已安装（见 docs/analysis.md）

#### 5.2 测试执行流程
1. **单个文件测试**：使用用例 A1、A2 验证基本功能
2. **小规模批量测试**：使用用例 A3 验证目录处理功能
3. **验证测试**：使用用例 A4 确认生成函数的正确性
4. **大规模测试**：使用用例 A5 验证系统稳定性
5. **复杂数据测试**：使用用例 A6 验证不同数据类型处理能力
6. **过拟合检测**：使用用例 E1 分析生成函数的泛化能力

#### 5.3 测试脚本修改指南
- **修改 tests/main.py 的基本步骤**：
  1. 注释/取消注释相应的函数调用
  2. 根据需要添加新的测试函数
  3. 修改文件路径以适应具体测试场景
- **常用修改模式**：
  ```python
  # 单文件测试模式
  if __name__ == '__main__':
      test_GenerateTDSFunction()

  # 目录批量处理模式
  if __name__ == '__main__':
      run_dir('../output/CS50_Same_City')

  # 验证模式
  if __name__ == '__main__':
      Common.check_pass_all('../output/CS50_Same_City')
  ```

### 6. 自动化与度量（基于现有脚本）

#### 6.1 测试结果度量标准
- **基础功能度量**：
  - 单文件处理成功率：生成 `.py` 文件且无异常
  - 批量处理成功率：`find . -name "*.passed" | wc -l` / `find . -name "*.txt" | wc -l`
  - 验证通过率：`Common.check_pass_all()` 输出的 ✅ 比例
- **生成函数质量度量**：
  - 训练样本通过率=100%（通过 `Common.check_pass_all()` 验证）
  - 函数代码合理性：包含逻辑判断而非硬编码枚举
  - 泛化能力：同类型新样本通过率 70-90%，异类型样本拒绝率 ≥80%
- **系统稳定性度量**：
  - 大规模处理无崩溃：`run_dir('../guangtie_params')` 正常完成
  - 内存使用稳定：处理过程中无 OOM 错误
  - 处理时间合理：单文件处理时间 < 2分钟（真实 LLM 环境）

#### 6.2 自动化测试脚本建议
基于现有 `tests/main.py` 创建自动化测试套件：
```python
# tests/automated_test.py
def run_test_suite():
    results = {}

    # 测试1：单文件处理
    results['single_file'] = test_GenerateTDSFunction()

    # 测试2：小规模批量处理
    results['batch_small'] = run_dir('../output/CS50_Same_City')

    # 测试3：验证生成函数
    results['validation'] = Common.check_pass_all('../output/CS50_Same_City')

    # 测试4：大规模处理（可选）
    # results['batch_large'] = run_dir('../guangtie_params')

    return results
```

#### 6.3 测试报告生成
- **成功率统计**：
  ```bash
  echo "处理成功文件数: $(find ../output -name "*.passed" | wc -l)"
  echo "总样本文件数: $(find ../output -name "*.txt" | wc -l)"
  echo "成功率: $(echo "scale=2; $(find ../output -name "*.passed" | wc -l) * 100 / $(find ../output -name "*.txt" | wc -l)" | bc)%"
  ```
- **验证结果统计**：通过分析 `Common.check_pass_all()` 的输出统计 ✅ 和 ❌ 的数量

### 7. 交付物与路径
- 本测试方案：docs/test_plan.md
- 参考分析与依赖：docs/analysis.md
- 审查报告：docs/test_plan_review.md
- **过拟合风险分析**：docs/overfitting_analysis.md（详细分析过拟合问题与改进建议）
- 测试新增脚本（建议）：按第6节所列在 tests/ 组织并配套 README 说明

### 8. 过拟合风险缓解建议（基于现有脚本测试）
基于过拟合风险分析，建议在实施测试时重点关注：
- **代码质量检查**：通过查看生成的 `.py` 文件内容，确保函数基于模式识别而非硬编码
- **泛化能力验证**：使用用例 E1 的方法，创建简单的泛化测试脚本验证生成函数
- **多样本测试**：选择不同类型的样本文件（数字、字符串、日期等）进行测试
- **批量验证**：使用 `Common.check_pass_all()` 确保所有生成的函数都能正确处理原始样本
- **手动抽查**：对关键的生成函数进行手动代码审查，确保逻辑合理性

### 9. 测试执行清单
为便于外部测试者执行，提供完整的测试步骤清单：

#### 9.1 环境检查
- [ ] 确认 LLM 服务可用（检查 Agent 配置）
- [ ] 确认项目依赖已安装
- [ ] 确认数据文件完整（`output/` 和 `guangtie_params/` 目录）

#### 9.2 基础功能测试
- [ ] 执行用例 A1：单文件测试
- [ ] 执行用例 A2：字符串样本测试
- [ ] 执行用例 A3：批量目录处理测试
- [ ] 执行用例 A4：验证生成函数正确性

#### 9.3 扩展测试
- [ ] 执行用例 A5：大规模目录处理（可选）
- [ ] 执行用例 A6：复杂数据类型测试
- [ ] 执行用例 E1：过拟合风险检测

#### 9.4 结果验证
- [ ] 检查生成文件数量和质量
- [ ] 统计成功率和验证通过率
- [ ] 分析生成函数的代码质量
- [ ] 记录测试过程中的异常和问题
