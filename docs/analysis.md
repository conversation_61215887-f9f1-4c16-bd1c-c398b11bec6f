## ProtocolExpert 项目分析报告

本报告对当前仓库进行全面代码分析，涵盖项目结构、用法（重点）、功能、配置与依赖。并附带典型使用示例与工作流说明，便于快速上手与扩展。

---

### 1. 项目结构与架构

- 顶层结构
  - data/ 存放示例 pcap 等数据
  - debug/ 运行调试输出
  - docs/ 分析与文档（本文件）
  - functions/ 自定义函数示例（如校验/解析）
  - guangtie_params/ new_params/ params/ sub_params/ 参数样例目录（按 exec/execute 子目录组织）
  - output/ 提取的 TDS Key/Value 数据输出目录（分子目录按 title）
  - src/ 核心源码
  - tests/ 示例与测试脚本
  - main.py 占位入口（当前未实现）

- 核心模块（src/）
  - Agent.py
    - Agent：基于 OpenAI 兼容 API 的对话/流式生成器；包含重复检测、长度限制、后处理等
    - Agent_DebugCode：针对代码报错自动“生成-运行-修复”循环
    - Agent_GenerateTDSFunction：基于样本值自动生成 check_valid 校验函数并迭代反馈修正
  - ChatBot.py
    - ToolAugmentedChatBot：支持从 LLM 回复中解析自定义工具调用（```tool_use ...```）并调用外部 MCP 服务
  - Common.py
    - 工具函数：读写 YAML/JSON、动态保存并导入函数、执行脚本、文本读取/分批、重复段落检测等
  - Prompt.py
    - Prompt：从 src/cfg/prompts.yaml 读取模板，根据 key 生成提示词
  - RepeatDetector.py
    - RepeatDetector：检测流式输出中重复片段，避免模型“循环”
  - PcapParser.py
    - PcapParser：基于 pyshark 解析 pcap，提取 TCP/TDS 数据、转码、过滤、回调驱动处理
  - TDS.py
    - TDS：与 TDS 协议字段格式相关的辅助（样例格式生成、路径信息提取）
  - TKV_summarizer.py
    - TKV_summarizer：消费 PcapParser 回调，解析“execute arith..XXX ...”样式调用，统计参数 Key/Value 并落盘
  - src/cfg/prompts.yaml
    - 提示词集合：remove_think、code_debug、call_function、gen_TDS_func（含 content/feedback/content_new 等）

- 测试与示例（tests/）
  - test_prompt.py：Prompt 读取与替换示例
  - test_code_debug.py：Agent_DebugCode 的“生成-执行-修复”示例
  - test_repeat.py：重复检测器示例
  - test_mcp.py：基于 FastAPI 的简化 MCP 工具服务示例（get_string_length）
  - MCP/tools_server.py 与 MCP/readme.txt：MCP 工具端/使用说明草稿
  - test_chat_with_history.py：期望演示 ChatBot 用法（但类名与实现不一致，见下“已知问题”）

---

### 2. 用法分析（重点）

以下按照三个主线场景说明：

- A. 基于样本自动“生成校验函数”的 Agent 工作流
- B. 从 PCAP 提取 TDS 调用并聚合参数的工作流
- C. 支持工具调用的对话机器人（MCP 简化版）

#### A. 生成 TDS 参数校验函数（Agent_GenerateTDSFunction）

核心类：src/Agent.py 中的 Agent_GenerateTDSFunction。

- 目标：给定一组“已知合法”的样本值，自动让 LLM 生成 check_valid(data)->bool 的函数，使之对样本全部返回 True，同时保持一定泛化能力；若初版不通过，自动反馈“未通过样本”并让模型修正，直至通过或达到重试上限。
- 关键依赖：
  - Prompt：使用 gen_TDS_func 的 content/content_new 与 feedback 模板
  - Common.save_and_import_function：将模型返回的代码保存为 .py 并动态导入
  - Agent_DebugCode：若生成代码存在语法/运行错误，自动用 code_debug prompt 进行修复循环
  - RepeatDetector：限制流式重复

最小用法（参考 tests/main.py 的 run_dir）：

1) 准备一组样本值文件，例如 output/XX/ 下的 .txt，每行一个样本（字符串需用引号包裹才能被 read_txt_with_dtype 读为 str）
2) 调用：
```
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common
from src.TDS import TDS

agent = Agent_GenerateTDSFunction()
data = Common.read_txt_with_dtype('path/to/samples.txt')
# 可选：根据路径推断 TDS 格式样例，辅助 prompt
# tds_info = TDS.get_info_form_path('.../XX/@param.txt', '.../XX')
# agent.shot_all(data, dst_dir='.', file_name='check_valid.py', tds_info=tds_info)
agent.shot_all(data, dst_dir='.', file_name='check_valid.py')
```
3) 产物：在 dst_dir 下生成 check_valid.py，内含 check_valid 函数；可直接 import 并批量验证。

注意：
- Agent.__init__ 需要可用的 OpenAI 兼容 API 服务（默认 base_url 指向本地 vLLM 网关），以及模型名（默认 Qwen3-32B-Int8）。
- 模型返回的代码通过 post_process 去除 markdown 包裹（```python ...```）。
- shot_all 会多轮抽样/反馈，直至通过或次数上限。

#### B. 从 PCAP 提取 TDS 调用并聚合参数（TKV_summarizer + PcapParser）

- 目标：从 PCAP 流量中，定位包含 “execute arith..XXX ...” 等模式的 TDS/TCP 负载；解析调用名（title）与参数列表，统计 key/value 并保存到 output/<title>/ 结构，以 txt 行方式累积样本，后续供 Agent 生成校验函数使用。
- 关键流程：
  - PcapParser.run_tds(callback)：基于 pyshark 提取 TDS 层数据（field: lang_language_text）并将文本回调给 summarizer
  - TKV_summarizer.run_pkg_execute_TDS(...)：
    - 识别调用前缀（exec/execute ... arith/window/center/web/extend 等变体）
    - 提取 title 与参数片段；区分有 key（@param=val）与无 key（纯值）两类
    - 将统计结构写入 self.info，并将 value 落盘至 save_kv_dir/<title>/ 下各 .txt 文件（去重在后处理 prost_preocess 中进行）

最小用法：
```
from src.TKV_summarizer import TKV_summarizer
from src.PcapParser import PcapParser

summ = TKV_summarizer(out_f='debug.json', use_tds=True, save_kv_dir='output')
parser = PcapParser('data/6703-5.pcap', server_port=6703)
parser.run_tds(summ.run_pkg_execute_TDS)
summ.prost_preocess()  # 写出 info JSON 与对 value 文件去重
```

产物：
- 输出 JSON：每个 title 对应一组统计项（key_cnt/key_names/value_cnt 等）
- 输出样本库：output/<title>/ 下若干 .txt，对应按键名或位置索引分桶的值样本集合

#### C. 工具增强对话机器人（ToolAugmentedChatBot + MCP 示例）

- 目标：让 LLM 回复中可以通过特定代码块声明工具调用参数，客户端解析后向 MCP 服务（FastAPI）发起 HTTP 请求获取结果，再将结果回灌给会话，继续生成。
- 关键点：
  - ChatBot.py 内的 ToolAugmentedChatBot：
    - chat(user_input) 会流式打印回复；若检测到 ```tool_use ...``` JSON，则解析 {"tool": "xxx", ...} 并调用 _call_tool(tool_name, **kwargs)
    - MCP 服务地址默认 http://localhost:8188（可改）
  - tests/test_mcp.py 与 tests/MCP/tools_server.py：提供基于 FastAPI 的工具服务样例，包含 /mcp-server/discovery 与具体工具端点实现

最小用法（交互）：
```
from src.ChatBot import ToolAugmentedChatBot

bot = ToolAugmentedChatBot(
    base_url="http://localhost:8000/v1",
    api_key="EMPTY",
    model="mistralai/Mistral-7B-Instruct-v0.1",
    mcp_server_url="http://localhost:8188"  # 你的工具服务地址
)
print(bot.chat("Hi"))
```

MCP 工具服务最小骨架（见 tests/test_mcp.py）：
- /mcp-server/discovery 提供工具清单
- /tool/<name> 提供实际工具逻辑（如调用本地 vLLM 或直接计算）

---

### 3. 功能与模块依赖关系

- Agent 系列
  - 依赖 OpenAI 兼容 API（vLLM/OpenAI/ModelScope 网关）
  - 依赖 Prompt（模板生成）、Common（代码保存与动态导入）、RepeatDetector（重复检测）
  - Agent_GenerateTDSFunction 额外依赖 Agent_DebugCode 与 TDS 提供的样例格式拼装

- TKV_summarizer 与 PcapParser
  - PcapParser 依赖 pyshark（系统需安装 tshark）进行离线 pcap 解析
  - TKV_summarizer 消费 PcapParser 回调，执行业务解析与样本聚合，落盘于 output/

- ChatBot 与 MCP
  - ToolAugmentedChatBot 依赖 requests 与 OpenAI 兼容 API
  - MCP 服务基于 FastAPI + pydantic + httpx（或直接本地计算）

- 数据/控制流（简图）
  1) PCAP -> PcapParser.run_tds -> TKV_summarizer.run_pkg_execute_TDS -> output/<title>/*.txt 样本
  2) 样本 -> Agent_GenerateTDSFunction.shot_all -> 生成 check_valid.py -> 校验样本 -> 失败样本反馈 -> 再生成
  3) ChatBot.chat -> LLM 回复含 ```tool_use ...``` -> _call_tool -> MCP HTTP -> 结果注入会话 -> 继续生成

---

### 4. 配置与依赖

- 提示词配置：src/cfg/prompts.yaml
  - 关键项：
    - code_debug：用于自动修复报错的函数生成
    - gen_TDS_func：用于生成校验函数的主模板；含 content/content_new 与 feedback 路径

- 外部服务配置：
  - Agent.__init__ 默认 base_url='http://127.0.0.1:8066/v1/'、model='Qwen3-32B-Int8'，并使用 api_key='<MODELSCOPE_SDK_TOKEN>'
  - ToolAugmentedChatBot 需要 base_url、model、mcp_server_url；工具服务需先启动

- 运行环境与第三方依赖（建议）：
  - Python >= 3.9
  - openai（用于 OpenAI 兼容 API 客户端）
  - requests（ChatBot 调用 MCP 工具）
  - pyyaml（读取 prompts.yaml）
  - loguru（日志封装）
  - tqdm（进度条）
  - pyshark（解析 pcap，需要系统安装 tshark）
  - fastapi、pydantic、uvicorn（MCP 工具服务）
  - httpx（示例 MCP 调用 vLLM）
  - numpy（TKV_summarizer 里导入但未强依赖）

示例 requirements（建议在项目根创建 requirements.txt，再用 pip 安装）：
```
openai>=1.30.0
requests>=2.31.0
PyYAML>=6.0.1
loguru>=0.7.2
tqdm>=4.66.0
pyshark>=0.6
fastapi>=0.111
pydantic>=2.7
uvicorn>=0.30
httpx>=0.27
numpy>=1.24
```
注意：安装 pyshark 前需先安装系统依赖 tshark（Linux: apt install tshark；macOS: brew install wireshark）。

---

### 5. 代码示例与常见工作流

- 5.1 解析 pcap 并生成样本库
```
from src.TKV_summarizer import TKV_summarizer
from src.PcapParser import PcapParser

summ = TKV_summarizer(out_f='debug/debug.json', use_tds=True, save_kv_dir='output')
parser = PcapParser('data/6703-5.pcap', server_port=6703)
parser.run_tds(summ.run_pkg_execute_TDS)
summ.prost_preocess()
```

- 5.2 基于样本生成校验函数
```
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common

data = Common.read_txt_with_dtype('output/CS50_Same_City/#0.txt')
agent = Agent_GenerateTDSFunction()
passed = agent.shot_all(data, dst_dir='debug', file_name='check_valid.py')
print('passed:', passed)
```

- 5.3 运行工具增强的 ChatBot 与 MCP 工具服务
```
# 终端1：启动工具服务（示例）
uvicorn tests.test_mcp:app --port 8188 --reload

# 终端2：启动对话
from src.ChatBot import ToolAugmentedChatBot
bot = ToolAugmentedChatBot(base_url="http://localhost:8000/v1", api_key="EMPTY", model="mistralai/Mistral-7B-Instruct-v0.1", mcp_server_url="http://localhost:8188")
print(bot.chat("请计算 add(1,2) 并用工具调用返回"))
```

---

### 6. 已知问题与改进建议

- tests/test_chat_with_history.py 中 `from src.ChatBot import ChatBot` 与实际实现类名 `ToolAugmentedChatBot` 不一致，需修正导入或添加 ChatBot 适配类。
- Common.py 中重复定义了 `check_repeat`（一个空实现、一个实际实现），建议保留后者并移除前者，避免混淆。
- TDS.get_info_form_path 使用 `path.strip(dir_prefix)` 可能不是预期（strip 是按字符集剥除），应使用 `os.path.relpath(path, dir_prefix)` 或前缀切片来获得相对路径。
- PcapParser.py 中 `parse_tds_custom` 定义出现重复且位置不同，建议合并。
- 依赖清单（requirements/pyproject）未随仓库提供，建议新增并在 README/文档中列出安装步骤。
- main.py 当前为空，可提供 CLI/子命令封装常见工作流：如 parse-pcap、gen-validator、chat。

---

### 7. 小结

本项目围绕三条主线：
- 从流量中抽取 TDS 调用与参数样本（数据侧）
- 基于样本自动生成“合法性校验函数”（模型侧）
- 借助 MCP 风格的工具调用增强 LLM 的可用性（工具侧）

模块职责划分清晰，具备端到端的原型能力。按上述建议完善依赖、修正小问题并增加 CLI/测试，可进一步提升可用性与可维护性。
