# ProtocolExpert 黑盒测试方案

## 概述

本测试方案专为外部测试者设计，基于项目现有数据结构和文件系统，通过"输入文件路径 → 执行命令 → 检查输出结果"的黑盒测试模式验证系统功能。

## 测试环境准备

### 前置条件
1. **系统依赖**：已安装 Python 3.9+、tshark
2. **Python 依赖**：已安装项目所需包（openai、pyshark、pyyaml 等）
3. **LLM 服务**：可用的 OpenAI 兼容 API 服务（本地 vLLM 或远程 API）
4. **数据文件**：项目现有的样本数据文件（guangtie_params/、output/ 目录）

### 测试数据说明
- **guangtie_params/**：原始参数样本文件，按 exec/execute → arith/center/web 等分类
- **output/**：已处理的样本数据，按 TDS 调用名称分目录，每个参数一个 .txt 文件
- **data/**：PCAP 流量文件（如 6703-5.pcap）

## 核心功能测试用例

### 测试用例 T1：基于现有样本生成校验函数（基础功能）

**测试目标**：验证系统能从现有样本数据自动生成有效的 check_valid 函数

**输入文件**：`output/CC60_PSR_query/@passenger_id_no.txt`
```
'130123199109245416'
```

**执行步骤**：
```bash
# 1. 进入项目根目录
cd /path/to/ProtocolExpert

# 2. 创建测试脚本
cat > test_t1.py << 'EOF'
import sys
sys.path.append('.')
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common
import os

# 读取样本数据
data = Common.read_txt_with_dtype('output/CC60_PSR_query/@passenger_id_no.txt')
print(f"读取到样本数据: {data}")

# 创建输出目录
os.makedirs('test_output/T1', exist_ok=True)

# 生成校验函数
agent = Agent_GenerateTDSFunction()
passed = agent.shot_all(data, dst_dir='test_output/T1', file_name='check_valid.py')

print(f"生成结果: {'成功' if passed else '失败'}")
if passed:
    print("生成的校验函数文件: test_output/T1/check_valid.py")
EOF

# 3. 执行测试
python test_t1.py
```

**验证标准**：
- 执行无异常退出（返回码 0）
- 控制台输出 "生成结果: 成功"
- 生成文件 `test_output/T1/check_valid.py` 存在
- 文件内容包含 `def check_valid(data):` 函数定义

**预期输出文件检查**：
```bash
# 检查生成的函数文件
ls -la test_output/T1/check_valid.py
cat test_output/T1/check_valid.py | grep "def check_valid"
```

---

### 测试用例 T2：多样本数据校验函数生成

**测试目标**：验证系统处理多个样本值的能力

**输入文件**：`output/CS50_Same_City/@train_date.txt`（包含多个日期样本）

**执行步骤**：
```bash
cat > test_t2.py << 'EOF'
import sys
sys.path.append('.')
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common
import os

# 读取多样本数据
data = Common.read_txt_with_dtype('output/CS50_Same_City/@train_date.txt')
print(f"读取到 {len(data)} 个样本: {data}")

# 创建输出目录
os.makedirs('test_output/T2', exist_ok=True)

# 生成校验函数
agent = Agent_GenerateTDSFunction()
passed = agent.shot_all(data, dst_dir='test_output/T2', file_name='date_validator.py')

print(f"生成结果: {'成功' if passed else '失败'}")

# 验证生成的函数
if passed:
    func = Common.import_function('test_output/T2', 'date_validator.py')
    print("测试生成的函数:")
    for sample in data:
        result = func(sample)
        print(f"  {sample} -> {result}")
EOF

python test_t2.py
```

**验证标准**：
- 生成成功且所有原始样本通过校验
- 生成的函数对训练样本返回 True
- 控制台输出显示每个样本的校验结果

---

### 测试用例 T3：TDS 格式信息集成测试

**测试目标**：验证系统利用 TDS 格式信息生成更准确校验函数的能力

**输入文件**：`guangtie_params/exec/arith/CC60_check_eticket/@db_name.txt`

**执行步骤**：
```bash
cat > test_t3.py << 'EOF'
import sys
sys.path.append('.')
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common
from src.TDS import TDS
import os

# 读取样本数据
input_file = 'guangtie_params/exec/arith/CC60_check_eticket/@db_name.txt'
data = Common.read_txt_with_dtype(input_file)
print(f"读取到样本数据: {data}")

# 获取 TDS 格式信息
tds_info = TDS.get_info_form_path(input_file, 'guangtie_params')
print(f"TDS 格式信息: {tds_info}")

# 创建输出目录
os.makedirs('test_output/T3', exist_ok=True)

# 生成校验函数（带 TDS 信息）
agent = Agent_GenerateTDSFunction()
passed = agent.shot_all(data, dst_dir='test_output/T3', file_name='tds_validator.py', tds_info=tds_info)

print(f"生成结果: {'成功' if passed else '失败'}")
print(f"生成文件: test_output/T3/tds_validator.py")
EOF

python test_t3.py
```

**验证标准**：
- TDS 格式信息正确解析
- 生成的校验函数考虑了 TDS 上下文
- 与不使用 TDS 信息的结果有差异（更精确）

---

### 测试用例 T4：批量目录处理测试

**测试目标**：验证系统批量处理多个参数文件的稳定性

**输入目录**：`guangtie_params/exec/arith/CS50_Same_City/`

**执行步骤**：
```bash
cat > test_t4.py << 'EOF'
import sys
sys.path.append('.')
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common
from src.TDS import TDS
import os
import glob

# 批量处理目录
input_dir = 'guangtie_params/exec/arith/CS50_Same_City'
output_dir = 'test_output/T4'
os.makedirs(output_dir, exist_ok=True)

agent = Agent_GenerateTDSFunction()
results = {}

# 处理目录下所有 .txt 文件
for file_path in glob.glob(os.path.join(input_dir, '*.txt')):
    print(f"\n处理文件: {file_path}")
    
    # 读取数据
    try:
        data = Common.read_txt_with_dtype(file_path)
        if not data:
            print(f"  跳过空文件")
            continue
            
        # 跳过包含逗号的字段（按原项目逻辑）
        if any([r.find(',') >= 0 for r in data if isinstance(r, str)]):
            print(f"  跳过包含逗号的字段")
            continue
            
        # 获取 TDS 信息
        tds_info = TDS.get_info_form_path(file_path, 'guangtie_params')
        
        # 生成校验函数
        filename = os.path.basename(file_path).replace('.txt', '.py')
        passed = agent.shot_all(data, dst_dir=output_dir, file_name=filename, tds_info=tds_info)
        
        results[file_path] = passed
        print(f"  结果: {'成功' if passed else '失败'}")
        
    except Exception as e:
        print(f"  错误: {e}")
        results[file_path] = False

# 输出汇总结果
print(f"\n=== 批量处理结果汇总 ===")
success_count = sum(1 for v in results.values() if v)
total_count = len(results)
print(f"成功: {success_count}/{total_count}")

for file_path, result in results.items():
    status = "✓" if result else "✗"
    print(f"{status} {os.path.basename(file_path)}")
EOF

python test_t4.py
```

**验证标准**：
- 批量处理不崩溃
- 成功率 ≥ 70%
- 每个成功的文件都生成对应的 .py 校验函数
- 输出目录包含生成的校验函数文件

---

### 测试用例 T5：校验函数检测正确性验证

**测试目标**：验证生成的校验函数能正确识别合法/非法输入

**基于**：T1 生成的校验函数

**执行步骤**：
```bash
cat > test_t5.py << 'EOF'
import sys
sys.path.append('.')
from src.Common import Common
import os

# 确保 T1 已执行并生成了校验函数
if not os.path.exists('test_output/T1/check_valid.py'):
    print("错误: 请先执行测试用例 T1")
    exit(1)

# 导入生成的校验函数
check_valid = Common.import_function('test_output/T1', 'check_valid.py')

# 测试数据集
test_cases = [
    # 合法样本（应该通过）
    ('130123199109245416', True, '原始训练样本'),
    ('110101199001011234', True, '其他合法身份证号'),
    ('320102198506123456', True, '不同地区身份证号'),
    
    # 非法样本（应该被拒绝）
    ('12345', False, '长度不足'),
    ('130123199109245416123', False, '长度过长'),
    ('13012319910924541a', False, '包含字母'),
    ('', False, '空字符串'),
    ('abc123def456ghi789', False, '完全非法格式'),
]

print("=== 校验函数检测正确性测试 ===")
correct_count = 0
total_count = len(test_cases)

for test_input, expected, description in test_cases:
    try:
        # 注意：输入需要加引号（按项目数据格式）
        quoted_input = f"'{test_input}'" if test_input else "''"
        result = check_valid(quoted_input)
        is_correct = (result == expected)
        
        status = "✓" if is_correct else "✗"
        print(f"{status} {description}: {quoted_input} -> {result} (期望: {expected})")
        
        if is_correct:
            correct_count += 1
            
    except Exception as e:
        print(f"✗ {description}: {test_input} -> 异常: {e}")

accuracy = correct_count / total_count * 100
print(f"\n准确率: {correct_count}/{total_count} ({accuracy:.1f}%)")

# 验证标准
if accuracy >= 70:
    print("✓ 检测正确性测试通过")
else:
    print("✗ 检测正确性测试失败")
EOF

python test_t5.py
```

**验证标准**：
- 原始训练样本通过率 = 100%
- 合法变体样本通过率 ≥ 80%
- 非法样本拦截率 ≥ 70%
- 总体准确率 ≥ 70%

---

### 测试用例 T6：PCAP 到校验函数端到端测试

**测试目标**：验证从 PCAP 解析到校验函数生成的完整流程

**输入文件**：`data/6703-5.pcap`（如果存在）

**执行步骤**：
```bash
cat > test_t6.py << 'EOF'
import sys
sys.path.append('.')
from src.TKV_summarizer import TKV_summarizer
from src.PcapParser import PcapParser
from src.Agent import Agent_GenerateTDSFunction
from src.Common import Common
import os
import glob

# 检查 PCAP 文件是否存在
pcap_file = 'data/6703-5.pcap'
if not os.path.exists(pcap_file):
    print(f"跳过测试: PCAP 文件 {pcap_file} 不存在")
    exit(0)

print("=== PCAP 到校验函数端到端测试 ===")

# 1. 解析 PCAP 生成样本
print("1. 解析 PCAP 文件...")
output_dir = 'test_output/T6_samples'
summ = TKV_summarizer(out_f='test_output/T6_debug.json', use_tds=True, save_kv_dir=output_dir)
parser = PcapParser(pcap_file, server_port=6703)
parser.run_tds(summ.run_pkg_execute_TDS)
summ.prost_preocess()

print(f"样本输出目录: {output_dir}")

# 2. 检查生成的样本文件
sample_files = glob.glob(os.path.join(output_dir, '*/*.txt'))
print(f"生成了 {len(sample_files)} 个样本文件")

if len(sample_files) == 0:
    print("警告: 未生成任何样本文件")
    exit(0)

# 3. 选择一个样本文件生成校验函数
test_file = sample_files[0]
print(f"3. 使用样本文件生成校验函数: {test_file}")

data = Common.read_txt_with_dtype(test_file)
if not data:
    print("样本文件为空，跳过")
    exit(0)

# 生成校验函数
agent = Agent_GenerateTDSFunction()
validator_dir = 'test_output/T6_validator'
os.makedirs(validator_dir, exist_ok=True)

passed = agent.shot_all(data, dst_dir=validator_dir, file_name='e2e_validator.py')

print(f"4. 校验函数生成: {'成功' if passed else '失败'}")

if passed:
    print("✓ 端到端测试通过")
    print(f"生成的校验函数: {validator_dir}/e2e_validator.py")
else:
    print("✗ 端到端测试失败")
EOF

python test_t6.py
```

**验证标准**：
- PCAP 解析成功生成样本文件
- 至少生成一个校验函数
- 整个流程无异常中断

---

## 测试执行指南

### 快速执行所有测试
```bash
# 创建测试执行脚本
cat > run_all_tests.sh << 'EOF'
#!/bin/bash
echo "开始执行 ProtocolExpert 黑盒测试..."

# 清理之前的测试输出
rm -rf test_output/
mkdir -p test_output/

# 执行各个测试用例
echo "=== 执行测试用例 T1: 基础功能 ==="
python test_t1.py

echo -e "\n=== 执行测试用例 T2: 多样本处理 ==="
python test_t2.py

echo -e "\n=== 执行测试用例 T3: TDS 格式集成 ==="
python test_t3.py

echo -e "\n=== 执行测试用例 T4: 批量处理 ==="
python test_t4.py

echo -e "\n=== 执行测试用例 T5: 检测正确性 ==="
python test_t5.py

echo -e "\n=== 执行测试用例 T6: 端到端测试 ==="
python test_t6.py

echo -e "\n=== 测试完成 ==="
echo "测试输出目录: test_output/"
ls -la test_output/
EOF

chmod +x run_all_tests.sh
./run_all_tests.sh
```

### 测试结果验证
```bash
# 检查生成的文件
find test_output/ -name "*.py" -exec echo "校验函数: {}" \;

# 检查测试日志
grep -r "成功\|失败\|✓\|✗" test_output/ || echo "在控制台输出中查看结果"
```

## 故障排除

### 常见问题及解决方案

1. **LLM 服务不可用**
   ```bash
   # 检查服务状态
   curl -X POST http://127.0.0.1:8066/v1/chat/completions \
     -H "Content-Type: application/json" \
     -d '{"model":"test","messages":[{"role":"user","content":"hello"}]}'
   ```

2. **依赖缺失**
   ```bash
   pip install openai requests PyYAML loguru tqdm pyshark
   ```

3. **权限问题**
   ```bash
   chmod -R 755 test_output/
   ```

4. **数据文件编码问题**
   ```bash
   file output/CS50_Same_City/@start_station.txt
   # 如果是非 UTF-8，需要转换编码
   ```

## 测试报告模板

执行完成后，可按以下格式整理测试报告：

```
ProtocolExpert 黑盒测试报告
测试时间: [日期]
测试环境: [操作系统/Python版本/LLM服务]

测试结果汇总:
- T1 基础功能: [通过/失败]
- T2 多样本处理: [通过/失败]  
- T3 TDS格式集成: [通过/失败]
- T4 批量处理: [通过/失败]
- T5 检测正确性: [通过/失败] (准确率: X%)
- T6 端到端测试: [通过/失败]

总体评估: [系统功能正常/存在问题]
```

这个黑盒测试方案完全基于项目现有的数据文件和接口，无需手动编写验证代码，适合外部测试者快速验证系统功能。
