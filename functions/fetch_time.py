import re

def is_valid_timestamp(timestamp):
    pattern = r'^(\d{1,2})-(\d{1,2})-(\d{4}) (\d{2}):(\d{2}):(\d{2})\.(\d{3})'

    match = re.fullmatch(pattern, timestamp)
    
    if not match:
        return False

    month, day, year, hour, minute, second, millisecond = map(int, match.groups())

    if not 1 <= month <= 12:
        return False
    if not 1 <= day <= 31:
        return False
    if not 0 <= hour <= 23:
        return False
    if not 0 <= minute <= 59:
        return False
    if not 0 <= second <= 59:
        return False
    if not 0 <= millisecond <= 999:
        return False

    return True

# 示例测试
print(is_valid_timestamp('4-7-2021 19:44:58.656'))  # True
print(is_valid_timestamp('4-7-2021 19:44:58.6560')) # False
print(is_valid_timestamp('13-7-2021 19:44:58.656')) # False
print(is_valid_timestamp('4-32-2021 19:44:58.656')) # False
print(is_valid_timestamp('4-7-2021 24:44:58.656'))  # False