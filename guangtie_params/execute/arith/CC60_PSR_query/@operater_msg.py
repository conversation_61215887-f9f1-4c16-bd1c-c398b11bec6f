
def check_valid(value):
    parts = value.split('#')
    if len(parts) != 10:
        return False

    # 第一个字段必须是 CKK 或 CKS
    if parts[0] not in ['CKK', 'CKS']:
        return False

    # 第二个字段必须是 Q
    if parts[1] != 'Q':
        return False

    # 第三个字段必须是两位数字，且在 60-69 范围内
    if len(parts[2]) != 2 or not parts[2].isdigit():
        return False
    if not (60 <= int(parts[2]) <= 69):
        return False

    # 第四个字段必须是三个大写字母
    if len(parts[3]) != 3 or not parts[3].isalpha() or not parts[3].isupper():
        return False

    # 第五个字段必须是七位数字
    if len(parts[4]) != 7 or not parts[4].isdigit():
        return False

    # 第六个字段必须是数字（不限制具体值）
    if not parts[5].isdigit():
        return False

    # 第七个字段必须是三位数字
    if len(parts[6]) != 3 or not parts[6].isdigit():
        return False

    # 第八个字段必须是 3 或 4 位，且所有字符为大写字母或数字
    if len(parts[7]) not in (3, 4):
        return False
    if not parts[7].isalnum():
        return False
    if not all(c.isupper() for c in parts[7] if c.isalpha()):
        return False

    # 第九个字段必须是一个字符，可以是大写字母或数字
    if len(parts[8]) != 1 or not (parts[8].isdigit() or parts[8].isalpha()):
        return False
    if parts[8].isalpha() and not parts[8].isupper():
        return False

    # 第十个字段必须是四位数字
    if len(parts[9]) != 4 or not parts[9].isdigit():
        return False

    return True
