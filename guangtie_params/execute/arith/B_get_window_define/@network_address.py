
def check_valid(value):
    if not isinstance(value, str):
        return False
    parts = value.split('.')
    if len(parts) != 4:
        return False
    for part in parts:
        # Check if part is numeric
        if not part.isdigit():
            return False
        # Check numeric range
        num = int(part)
        if num < 0 or num > 255:
            return False
        # Check for leading zeros (except single zero)
        if len(part) > 1 and part[0] == '0':
            return False
    return True
