
def check_valid(value):
    # 检查是否为字符串类型
    if not isinstance(value, str):
        return False
    
    # 允许空字符串（根据示例数据）
    if value == "":
        return True
    
    # 检查长度是否在合理范围内（1-50字符）
    if len(value) < 1 or len(value) > 50:
        return False
    
    # 检查是否仅包含允许的字符：字母（大小写）、数字、空格、下划线、连字符
    allowed_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 _-")
    for c in value:
        if c not in allowed_chars:
            return False
    
    return True
