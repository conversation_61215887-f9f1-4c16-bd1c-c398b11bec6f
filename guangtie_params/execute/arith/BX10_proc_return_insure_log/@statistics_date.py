
def check_valid(value):
    # 检查输入类型是否为字符串
    if not isinstance(value, str):
        return False
    
    # 检查长度是否为8位
    if len(value) != 8:
        return False
    
    # 检查是否全部由数字组成
    if not value.isdigit():
        return False
    
    # 拆分年月日部分
    year = int(value[0:4])
    month = int(value[4:6])
    day = int(value[6:8])
    
    # 年份范围检查（合理日期范围）
    if not (1900 <= year <= 2100):
        return False
    
    # 月份范围检查
    if not (1 <= month <= 12):
        return False
    
    # 日期范围检查（不验证具体月份天数，保持宽松）
    if not (1 <= day <= 31):
        return False
    
    return True
