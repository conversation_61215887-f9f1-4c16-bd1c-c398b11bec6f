
def check_valid(value):
    if not isinstance(value, str):
        return False
    if len(value) != 30:
        return False
    if not value.startswith("Q160IXQ"):
        return False
    middle_digits = value[7:25]
    if not middle_digits.isdigit():
        return False
    if len(middle_digits) != 18:
        return False
    if value[25] != 'R':
        return False
    last_part = value[26:30]
    if not last_part.isdigit() or len(last_part) != 4:
        return False
    return True
