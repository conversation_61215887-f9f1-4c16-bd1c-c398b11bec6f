
def check_valid(value):
    # 检查输入是否为字符串类型
    if not isinstance(value, str):
        return False
    # 仅检查长度不超过100（允许空字符串）
    if len(value) > 100:
        return False
    # 允许的字符集：字母、数字、中文、空格、下划线、连字符
    allowed_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 _-")
    for char in value:
        if not (char in allowed_chars or '\u4e00' <= char <= '\u9fa5'):
            return False
    return True
