
def check_valid(value):
    # 检查是否为字符串类型
    if not isinstance(value, str):
        return False
    
    # 检查长度是否为8
    if len(value) != 8:
        return False
    
    # 检查是否全为数字
    if not value.isdigit():
        return False
    
    # 拆分年月日部分
    year = int(value[0:4])
    month = int(value[4:6])
    day = int(value[6:8])
    
    # 检查月份范围
    if not (1 <= month <= 12):
        return False
    
    # 检查日期范围
    if not (1 <= day <= 31):
        return False
    
    return True
