
def check_valid(value: str) -> bool:
    """
    检查给定值是否符合TDS协议中CC60_check_telephoneInfo请求参数的语义特征
    
    语义特征:
    1. 必须是长度为14的字符串
    2. 首字符必须是小写字母c
    3. 剩余13个字符必须是小写字母或数字的组合
    4. 不允许包含大写字母或特殊字符
    
    参数:
        value (str): 需要验证的字符串
        
    返回:
        bool: 如果符合特征返回True，否则返回False
    """
    if not isinstance(value, str):
        return False
        
    if len(value) != 14:
        return False
        
    if value[0] != 'c':
        return False
        
    for ch in value[1:]:
        if not (ch.isdigit() or (ch.isalpha() and ch.islower())):
            return False
            
    return True
