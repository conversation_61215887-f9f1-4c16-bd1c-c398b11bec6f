
def check_valid(id_str):
    if not isinstance(id_str, str) or len(id_str) != 18:
        return False
    if not id_str[:17].isdigit():
        return False
    if id_str[17] not in '0123456789Xx':
        return False
    birth_date = id_str[6:14]
    if len(birth_date) != 8 or not birth_date.isdigit():
        return False
    month = int(birth_date[4:6])
    day = int(birth_date[6:])
    if not (1 <= month <= 12) or not (1 <= day <= 31):
        return False
    return True
