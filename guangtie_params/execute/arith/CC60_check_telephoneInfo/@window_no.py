
def check_valid(value):
    if isinstance(value, int):
        return value > 0
    if isinstance(value, str):
        if value.isdigit() and value[0] != '0' or value == '0':
            return int(value) > 0
        if value.count('.') == 1:
            left, right = value.split('.')
            if right == '0' and left.isdigit() and left[0] != '0' or left == '0':
                return int(left) > 0
    return False
