
def check_valid(value):
    """
    合法性检查函数，用于验证TDS协议中$VALUE字段的合法性
    参数:
        value (str): 需要验证的值
    返回:
        bool: True表示合法，False表示非法
    """
    # 首先确保是字符串类型
    if not isinstance(value, str):
        return False
        
    # 允许空字符串作为合法值
    if value == "":
        return True
        
    # 针对可能的身份证号/护照号等证件号码场景
    # 允许的字符集：数字、字母、连字符、下划线
    # 长度限制在5-30字符之间（覆盖常见证件号长度）
    if 5 <= len(value) <= 30:
        return all(c.isalnum() or c in '-_' for c in value)
        
    # 针对可能的日期/时间格式（YYYY-MM-DD等）
    if len(value) == 10 and value.count('-') == 2:
        try:
            parts = value.split('-')
            if len(parts) == 3 and all(part.isdigit() for part in parts):
                return True
        except:
            pass
            
    # 针对可能的纯数字场景（年龄、数量等）
    if value.isdigit():
        return True
        
    # 针对可能的订单号/流水号（字母数字组合）
    if all(c.isalnum() for c in value) and 8 <= len(value) <= 20:
        return True
        
    return False
