
def check_valid(value):
    """
    Check if the given value is a valid station telecode.
    
    Valid values are strings of exactly 3 uppercase English letters.
    
    Args:
        value: The value to check.
        
    Returns:
        bool: True if the value is valid, False otherwise.
    """
    # Check if the value is a string
    if not isinstance(value, str):
        return False
    
    # Check the length is exactly 3 characters
    if len(value) != 3:
        return False
    
    # Check all characters are uppercase English letters
    return all(char.isalpha() and char.isupper() for char in value)
