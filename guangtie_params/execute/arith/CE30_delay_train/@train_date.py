
def check_valid(value):
    if not isinstance(value, str):
        return False
    if len(value) != 8:
        return False
    if not value.isdigit():
        return False
    year = int(value[:4])
    month = int(value[4:6])
    day = int(value[6:])
    
    if not (2000 <= year <= 2099):
        return False
    if not (1 <= month <= 12):
        return False
    if not (1 <= day <= 31):
        return False
        
    return True
