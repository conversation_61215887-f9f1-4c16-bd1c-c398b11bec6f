
def check_valid(value):
    if is_valid_ip(value):
        return True
    if is_valid_hex(value):
        return True
    return False

def is_valid_ip(ip):
    parts = ip.split('.')
    if len(parts) != 4:
        return False
    for part in parts:
        if not part.isdigit():
            return False
        num = int(part)
        if num < 0 or num > 255:
            return False
    return True

def is_valid_hex(hex_str):
    if len(hex_str) != 14:
        return False
    if not hex_str.startswith('c68'):
        return False
    valid_chars = set('0123456789abcdef')
    return all(c in valid_chars for c in hex_str.lower())
