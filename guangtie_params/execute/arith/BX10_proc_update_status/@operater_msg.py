
import re

def check_valid(value):
    # 检查是否为字符串类型
    if not isinstance(value, str):
        return False
    
    # 检查长度是否在合理范围内（根据样本数据估算）
    if len(value) < 20 or len(value) > 30:
        return False
    
    # 检查是否全部由大写字母和数字组成
    if not value.isalnum():
        return False
    
    # 检查是否以Q开头
    if not value.startswith('Q'):
        return False
    
    # 检查是否包含IXQ作为中间标识符
    if 'IXQ' not in value:
        return False
    
    # 检查是否以R后跟4位数字结尾
    if len(value) - value.rfind('R') != 5:
        return False
    if not value[-4:].isdigit():
        return False
    
    # 组合正则表达式检查整体结构
    pattern = r'^Q[A-Z0-9]*IXQ[A-Z0-9]*R\d{4}$'
    if not re.match(pattern, value):
        return False
    
    return True
