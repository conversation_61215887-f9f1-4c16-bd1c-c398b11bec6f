
import datetime

def check_valid(value):
    # 检查是否为字符串类型
    if not isinstance(value, str):
        return False
    # 检查长度是否为8位
    if len(value) != 8:
        return False
    # 检查是否全部为数字
    if not value.isdigit():
        return False
    # 验证是否为有效日期
    try:
        year = int(value[0:4])
        month = int(value[4:6])
        day = int(value[6:8])
        datetime.date(year, month, day)
        return True
    except ValueError:
        return False
