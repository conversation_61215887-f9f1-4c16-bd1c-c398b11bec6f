
def check_valid(s):
    # 检查总长度是否为30字符
    if len(s) != 30:
        return False
    
    # 检查第一个字符是否为Q
    if s[0] != 'Q':
        return False
    
    # 检查第2到第7字符是否为字母或数字（共6个字符）
    if not s[1:7].isalnum():
        return False
    
    # 检查第8到第25字符是否为数字（共18个字符）
    if not s[7:25].isdigit():
        return False
    
    # 检查第26字符是否为R
    if s[25] != 'R':
        return False
    
    # 检查最后四位是否为数字
    if not s[26:].isdigit() or len(s[26:]) != 4:
        return False
    
    return True
