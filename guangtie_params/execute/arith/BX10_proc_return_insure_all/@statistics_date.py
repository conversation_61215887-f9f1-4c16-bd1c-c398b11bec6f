
def check_valid(value):
    if not isinstance(value, str) or len(value) != 8:
        return False
    
    if not value.isdigit():
        return False
    
    year = int(value[:4])
    month = int(value[4:6])
    day = int(value[6:8])
    
    if month < 1 or month > 12:
        return False
    
    def is_leap_year(y):
        return (y % 4 == 0 and y % 100 != 0) or (y % 400 == 0)
    
    month_days = {
        1: 31, 2: 29 if is_leap_year(year) else 28,
        3: 31, 4: 30, 5: 31, 6: 30,
        7: 31, 8: 31, 9: 30, 10: 31,
        11: 30, 12: 31
    }
    
    max_day = month_days.get(month, 0)
    
    return 1 <= day <= max_day
