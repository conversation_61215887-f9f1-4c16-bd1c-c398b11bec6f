
def check_valid(value):
    # 检查长度是否为8位
    if len(value) != 8:
        return False
    
    # 检查是否全由数字组成
    if not value.isdigit():
        return False
    
    # 分解年月日
    year = int(value[0:4])
    month = int(value[4:6])
    day = int(value[6:8])
    
    # 检查月份范围
    if month < 1 or month > 12:
        return False
    
    # 检查日期范围（宽松检查）
    if day < 1 or day > 31:
        return False
    
    return True
