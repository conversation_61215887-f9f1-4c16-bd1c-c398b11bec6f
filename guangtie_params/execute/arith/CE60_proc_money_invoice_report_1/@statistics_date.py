
import datetime

def check_valid(value):
    """
    检查输入值是否符合YYYYMMDD格式的合法日期
    :param value: 输入值（字符串或可转换为字符串的类型）
    :return: 如果合法返回True，否则返回False
    """
    if not isinstance(value, str):
        value = str(value)
    
    # 检查长度是否为8且全部由数字组成
    if len(value) != 8 or not value.isdigit():
        return False
    
    year_str = value[:4]
    month_str = value[4:6]
    day_str = value[6:8]
    
    try:
        year = int(year_str)
        month = int(month_str)
        day = int(day_str)
        
        # 检查月份范围
        if month < 1 or month > 12:
            return False
            
        # 检查日期范围
        if day < 1:
            return False
            
        # 使用datetime模块验证日期有效性（自动处理闰年、各月天数差异）
        datetime.datetime(year, month, day)
        return True
        
    except (ValueError, IndexError):
        return False
