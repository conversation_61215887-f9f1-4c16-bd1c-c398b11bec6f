
def check_valid(value):
    """
    Check if a value is valid based on the observed patterns in the dataset.
    
    Args:
        value: The value to check
        
    Returns:
        bool: True if the value is valid, False otherwise
    """
    # Check if the input is an integer
    if not isinstance(value, int):
        return False
        
    # Check if the value is within the observed range
    return 0 <= value <= 44520
