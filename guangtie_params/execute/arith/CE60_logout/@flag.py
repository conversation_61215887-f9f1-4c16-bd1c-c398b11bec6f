
def check_valid(value):
    """
    检查给定的值是否符合TDS协议payload中$VALUE的格式要求
    
    参数:
        value: 要检查的值
        
    返回:
        如果值合法返回True，否则返回False
    """
    # 检查输入是否为字符串
    if not isinstance(value, str):
        return False
    
    # 检查是否只包含数字字符
    if not value.isdigit():
        return False
    
    # 转换为整数
    try:
        num = int(value)
    except ValueError:
        return False
    
    # 已知合法值的范围
    valid_values = {1, 2}
    
    # 检查是否在合法值范围内
    return num in valid_values
