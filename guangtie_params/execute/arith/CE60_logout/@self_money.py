
def check_valid(value):
    """
    Check if the given value is valid based on TDS protocol payload patterns.
    Rules:
    1. Must be an integer type
    2. Must be non-negative (>= 0)
    3. Must be within reasonable bounds for typical financial operations (0-1000000)
    """
    if not isinstance(value, int):
        return False
    if value < 0:
        return False
    if value > 1000000:
        return False
    return True
