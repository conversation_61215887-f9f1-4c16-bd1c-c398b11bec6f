
def check_valid(value: str) -> bool:
    """
    检查给定的值是否符合TDS协议中$VALUE字段的格式要求
    
    格式要求：
    1. 必须是长度为10的字符串
    2. 必须以"8E"开头
    3. 后续8个字符必须是数字
    
    参数:
        value (str): 要检查的值
    
    返回:
        bool: 如果值符合格式要求则返回True，否则返回False
    """
    if len(value) != 10:
        return False
        
    if not value.startswith("8E"):
        return False
        
    if not value[2:].isdigit():
        return False
        
    return True
