
def check_valid(value: str) -> bool:
    """
    验证给定值是否符合TDS协议中提取的$VALUE字段格式要求
    
    Args:
        value: 要验证的字符串值
        
    Returns:
        如果值符合格式要求返回True，否则返回False
    """
    # 规则1: 长度应在7到18位之间
    if not (7 <= len(value) <= 18):
        return False
    
    # 规则2: 仅包含数字和大写字母
    if not all(c.isdigit() or (c.isalpha() and c.isupper()) for c in value):
        return False
    
    # 规则3: 开头不能是0（除非是7位的特殊情况）
    if len(value) > 7 and value[0] == '0':
        return False
    
    # 规则4: 最后一位可以是数字或X
    if not (value[-1].isdigit() or value[-1] == 'X'):
        return False
    
    # 规则5: 至少包含一些数字（不能全是字母）
    if not any(c.isdigit() for c in value):
        return False
        
    return True
