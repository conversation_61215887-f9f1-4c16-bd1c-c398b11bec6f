
import datetime

def check_valid(value: str) -> bool:
    # 检查以###结尾
    if not value.endswith("###"):
        return False
    
    # 检查长度至少为8+3=11
    if len(value) < 11:
        return False
    
    # 检查前8位是否为有效日期 (YYYYMMDD)
    date_str = value[0:8]
    try:
        datetime.datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        return False
    
    # 检查中间部分是否包含允许的字符 (字母和数字)
    middle_part = value[8:-3]
    allowed_chars = set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")
    for c in middle_part:
        if c not in allowed_chars:
            return False
    
    return True
