
def check_valid(value):
    """
    检查字符串是否符合TDS协议中特定格式的合法性要求
    
    参数:
        value (str): 需要检查的字符串
        
    返回:
        bool: 如果字符串格式合法返回True，否则返回False
    """
    # 检查字符串长度是否为30
    if len(value) != 30:
        return False
        
    # 检查是否以固定前缀开头
    if not value.startswith('Q160IXQ'):
        return False
        
    # 检查中间18位数字部分
    numeric_part = value[7:25]
    if len(numeric_part) != 18 or not numeric_part.isdigit():
        return False
        
    # 检查中间分隔符R
    if value[25] != 'R':
        return False
        
    # 检查结尾4位数字
    end_part = value[26:]
    if len(end_part) != 4 or not end_part.isdigit():
        return False
        
    return True
