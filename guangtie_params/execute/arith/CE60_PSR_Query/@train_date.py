
def check_valid(value):
    """
    验证值是否符合日期格式要求（YYYYMMDD）
    
    参数:
        value: 要验证的值
        
    返回:
        bool: 如果值符合要求返回True，否则返回False
    """
    # 检查是否为字符串类型
    if not isinstance(value, str):
        return False
        
    # 检查长度是否为8位
    if len(value) != 8:
        return False
        
    # 检查是否全部由数字组成
    if not value.isdigit():
        return False
        
    # 检查年份范围（1900-2100）
    year = int(value[0:4])
    if not (1900 <= year <= 2100):
        return False
        
    # 检查月份范围（01-12）
    month = int(value[4:6])
    if not (1 <= month <= 12):
        return False
        
    # 检查日期范围（01-31）
    day = int(value[6:8])
    if not (1 <= day <= 31):
        return False
        
    return True
