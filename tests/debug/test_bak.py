
def check_valid(data):
    # 合法数据示例
    valid_data = {
        "<001003003014084>",
        "<001003003014057>",
        "<>"
    }

    # 检查是否为字符串
    if not isinstance(data, str):
        return False

    # 去除可能的首尾空格
    data = data.strip()

    # 检查是否以 '<' 开头，以 '>' 结尾
    if not (data.startswith("<") and data.endswith(">")):
        return False

    # 检查是否在合法数据集合中
    if data in valid_data:
        return True

    # 检查是否为 "<>" 的情况
    if data == "<>":
        return True

    # 检查中间内容是否为数字
    content = data[1:-1]
    if content and not content.isdigit():
        return False

    # 检查长度是否在合理范围内（根据示例数据）
    if len(content) < 0 or len(content) > 15:
        return False

    return True
