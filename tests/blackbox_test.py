#!/usr/bin/env python3
"""
ProtocolExpert 黑盒测试脚本
基于项目现有文件结构进行端到端测试，无需手动编写验证代码
"""

import sys
import os
import json
import glob
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.Common import Common
from src.Agent import Agent_GenerateTDSFunction
from src.TDS import TDS


def test_single_file(input_file, output_dir="test_output", use_tds_info=True):
    """
    测试单个样本文件的校验函数生成
    
    Args:
        input_file: 输入样本文件路径
        output_dir: 输出目录
        use_tds_info: 是否使用TDS信息
    
    Returns:
        dict: 测试结果
    """
    result = {
        'input_file': input_file,
        'success': False,
        'generated_file': None,
        'passed_file': None,
        'sample_count': 0,
        'validation_passed': False,
        'error': None
    }
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            result['error'] = f"输入文件不存在: {input_file}"
            return result
        
        # 读取样本数据
        data = Common.read_txt_with_dtype(input_file)
        if not data:
            result['error'] = "样本文件为空"
            return result
        
        result['sample_count'] = len(data)
        
        # 跳过包含逗号的字段（按原项目逻辑）
        if any([r.find(',') >= 0 for r in data if isinstance(r, str)]):
            result['error'] = "跳过包含逗号的字段"
            return result
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取TDS信息（如果需要）
        tds_info = None
        if use_tds_info:
            try:
                # 尝试从guangtie_params推断TDS信息
                tds_info = TDS.get_info_form_path(input_file, 'guangtie_params')
            except:
                # 如果失败，尝试从output推断
                try:
                    tds_info = TDS.get_info_form_path(input_file, 'output')
                except:
                    pass
        
        # 生成校验函数
        agent = Agent_GenerateTDSFunction()
        filename = os.path.basename(input_file).replace('.txt', '.py')
        output_file = os.path.join(output_dir, filename)
        
        passed = agent.shot_all(data, dst_dir=output_dir, file_name=filename, tds_info=tds_info)
        
        result['success'] = passed
        result['generated_file'] = output_file if passed else None
        
        # 如果生成成功，创建.passed标记文件
        if passed:
            passed_file = os.path.join(output_dir, os.path.basename(input_file).replace('.txt', '.passed'))
            with open(passed_file, 'w') as f:
                f.write('Generated successfully')
            result['passed_file'] = passed_file
            
            # 验证生成的函数是否能正确处理所有样本
            if os.path.exists(output_file):
                try:
                    func = Common.import_function(output_dir, filename)
                    validation_results = []
                    for sample in data:
                        validation_results.append(func(sample))
                    result['validation_passed'] = all(validation_results)
                except Exception as e:
                    result['error'] = f"函数验证失败: {str(e)}"
        
    except Exception as e:
        result['error'] = str(e)
    
    return result


def test_directory(input_dir, output_dir="test_output", max_files=None):
    """
    测试目录下所有样本文件
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录  
        max_files: 最大测试文件数（None表示全部）
    
    Returns:
        dict: 测试结果汇总
    """
    results = {
        'total_files': 0,
        'successful_files': 0,
        'failed_files': 0,
        'skipped_files': 0,
        'details': []
    }
    
    # 查找所有.txt文件
    pattern = os.path.join(input_dir, '**/*.txt')
    txt_files = glob.glob(pattern, recursive=True)
    
    if max_files:
        txt_files = txt_files[:max_files]
    
    results['total_files'] = len(txt_files)
    
    for txt_file in txt_files:
        print(f"处理文件: {txt_file}")
        
        # 检查是否已经处理过
        passed_file = txt_file.replace('.txt', '.passed')
        if os.path.exists(passed_file):
            print(f"  已处理，跳过")
            results['skipped_files'] += 1
            continue
        
        result = test_single_file(txt_file, output_dir)
        results['details'].append(result)
        
        if result['success']:
            results['successful_files'] += 1
            print(f"  ✅ 成功")
        else:
            results['failed_files'] += 1
            print(f"  ❌ 失败: {result['error']}")
    
    return results


def validate_generated_functions(test_dir):
    """
    验证已生成的校验函数是否正确工作
    
    Args:
        test_dir: 测试目录
    
    Returns:
        dict: 验证结果
    """
    results = {
        'total_functions': 0,
        'valid_functions': 0,
        'invalid_functions': 0,
        'details': []
    }
    
    # 查找所有.py文件
    py_files = glob.glob(os.path.join(test_dir, '*.py'))
    results['total_functions'] = len(py_files)
    
    for py_file in py_files:
        txt_file = py_file.replace('.py', '.txt')
        
        result = {
            'py_file': py_file,
            'txt_file': txt_file,
            'valid': False,
            'error': None
        }
        
        try:
            if os.path.exists(txt_file):
                # 读取原始样本数据
                data = Common.read_txt_with_dtype(txt_file)
                
                # 导入生成的函数
                func = Common.import_function(os.path.dirname(py_file), os.path.basename(py_file))
                
                # 验证所有样本
                validation_results = []
                for sample in data:
                    validation_results.append(func(sample))
                
                result['valid'] = all(validation_results)
                if result['valid']:
                    results['valid_functions'] += 1
                else:
                    results['invalid_functions'] += 1
                    result['error'] = "部分样本验证失败"
            else:
                result['error'] = f"对应的txt文件不存在: {txt_file}"
                results['invalid_functions'] += 1
                
        except Exception as e:
            result['error'] = str(e)
            results['invalid_functions'] += 1
        
        results['details'].append(result)
        print(f"验证 {py_file}: {'✅' if result['valid'] else '❌'} {result['error'] or ''}")
    
    return results


def main():
    parser = argparse.ArgumentParser(description='ProtocolExpert 黑盒测试工具')
    parser.add_argument('--mode', choices=['single', 'directory', 'validate'], required=True,
                        help='测试模式: single(单文件), directory(目录), validate(验证)')
    parser.add_argument('--input', required=True, help='输入文件或目录路径')
    parser.add_argument('--output', default='test_output', help='输出目录')
    parser.add_argument('--max-files', type=int, help='最大测试文件数')
    parser.add_argument('--no-tds-info', action='store_true', help='不使用TDS信息')
    parser.add_argument('--json-output', help='将结果保存为JSON文件')
    
    args = parser.parse_args()
    
    if args.mode == 'single':
        result = test_single_file(args.input, args.output, not args.no_tds_info)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    elif args.mode == 'directory':
        result = test_directory(args.input, args.output, args.max_files)
        print(f"\n测试完成:")
        print(f"  总文件数: {result['total_files']}")
        print(f"  成功: {result['successful_files']}")
        print(f"  失败: {result['failed_files']}")
        print(f"  跳过: {result['skipped_files']}")
        
    elif args.mode == 'validate':
        result = validate_generated_functions(args.input)
        print(f"\n验证完成:")
        print(f"  总函数数: {result['total_functions']}")
        print(f"  有效: {result['valid_functions']}")
        print(f"  无效: {result['invalid_functions']}")
    
    # 保存JSON结果
    if args.json_output:
        with open(args.json_output, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {args.json_output}")


if __name__ == '__main__':
    main()
