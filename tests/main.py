import sys
sys.path.append('..')
import os
import glob
from tqdm import tqdm

import importlib
from src.Common import Common
from src.Agent import Agent_GenerateTDSFunction,Agent_DebugCode
from src.TDS import TDS




def test_GenerateTDSFunction():
    agent = Agent_GenerateTDSFunction() 
    data = Common.read_txt('../output/B_get_admin_auth/@admin_code.txt')
    agent.shot_all(data,dst_dir='debug',file_name='test1.py')


def run_dir(in_dir):
    agent = Agent_GenerateTDSFunction() 
    
    for file in tqdm(glob.glob(os.path.join(in_dir,'**/*.txt'),recursive=True)):
        p_file = os.path.splitext(file)[0]+'.passed'
        print ('---------p_file:',p_file)

        if os.path.exists(p_file) == True:
            print ('{} have passed!'.format(file))

        tds_info = TDS.get_info_form_path(file,in_dir)
        # print ('tds_info:',tds_info)

        data = Common.read_txt_with_dtype(file)
        # 先跳过带，号的字段
        if any([r.find(',') >= 0 for r in data if isinstance(r,str)==True]) == True:
            continue

        path,filename = os.path.split(file)

        passed = agent.shot_all(data,dst_dir=path,file_name=filename.replace('.txt','.py'), tds_info=tds_info)
        if passed == True:
            with open(p_file,'w+') as fw:
                print ('save ---------------',p_file)

if __name__ == '__main__':

    # test_GenerateTDSFunction()

    # test_code_debug()

    # test_dir('../params/exec/center/JP33_check_ticket_info')
    run_dir('../output/B_get_admin_auth')

    # Common.check_pass_all('../guangtie_params')
    




