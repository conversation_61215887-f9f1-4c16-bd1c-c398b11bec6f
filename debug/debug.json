{"CS50_Same_City": [{"key_cnt": 3, "value_cnt": 3, "key_names": ["@train_date", "@start_station", "@to_station"]}], "YY40_get_notice": [{"key_cnt": 0, "key_names": null, "value_cnt": 5}], "DS50_Fetch_Train_Info": [{"key_cnt": 0, "key_names": null, "value_cnt": 13}], "B_get_admin_auth": [{"key_cnt": 8, "value_cnt": 8, "key_names": ["@admin_date", "@inner_code", "@admin_code", "@up_admin_code", "@up_inner_code", "@up_admin_name", "@up_admin_location", "@out_flag"]}], "DS60_uncommon_word": [{"key_cnt": 1, "value_cnt": 1, "key_names": ["@name"]}], "CC60_PSR_query": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@query_type", "@passenger_id_type", "@passenger_id_no", "@passenger_name", "@operater_msg"]}], "CS30_Fetch_Stop_Time": [{"key_cnt": 4, "value_cnt": 4, "key_names": ["@board_train_date", "@board_train_code", "@board_station_name", "@directions"]}], "CS50_Adjust_Window_Range": [{"key_cnt": 7, "value_cnt": 7, "key_names": ["@application_msg", "@operater_msg", "@ticket_office", "@special_train", "@TK_Info_Kind", "@seat_msg", "@last_time"]}], "DS60_proc_query_print": [{"key_cnt": 6, "value_cnt": 6, "key_names": ["@operater_msg", "@eticket_no", "@sequence_no", "@new_flag", "@application_msg", "@window_para"]}], "DS60_window_modify_eticket": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@application_msg", "@operater_msg", "@modify_type", "@ext_ticket_no", "@sequence_no"]}], "CT50_insurecz_cs4_hz;1": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@start_date", "@end_date", "@inner_code", "@office_no", "@statist_name"]}], "CS30_BSR_delete": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@application_msg", "@operater_msg", "@fetch_time", "@seat_msg", "@flag"]}], "DS30_Ticket_Cancel": [{"key_cnt": 0, "key_names": null, "value_cnt": 10}], "CT60_insurecz_cs4_operater;1": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@start_date", "@end_date", "@inner_code", "@office_no", "@statist_name"]}], "DS60_eticket_sale_check": [{"key_cnt": 12, "value_cnt": 12, "key_names": ["@application_msg", "@operater_msg", "@tk_info_kind", "@train_date", "@train_no", "@train_code", "@from_telecode", "@to_telecode", "@inner_code", "@sequence_no", "@invoice_state", "@old_seat_msg"]}], "DS40_Ticket_Fetch": [{"key_cnt": 0, "key_names": null, "value_cnt": 21}], "CS40_Ticket_Write": [{"key_cnt": 0, "key_names": null, "value_cnt": 14}], "CG10_check_id_info": [{"key_cnt": 6, "value_cnt": 6, "key_names": ["@operater_msg", "@id_type", "@id_no", "@id_name", "@ticket_type", "@kind"]}], "DS52_Proc_Rule_Info": [{"key_cnt": 17, "value_cnt": 17, "key_names": ["@id_type", "@id_number", "@train_date", "@train_no", "@board_station", "@to_station", "@kind", "@check_flag", "@ticket_type", "@seat_type_code", "@sale_mode", "@coach_no", "@seat_no", "@operater_msg", "@ticket_no", "@resign_seat_msg", "@ticket_price"]}], "DS60_608_follow_rule": [{"key_cnt": 3, "value_cnt": 3, "key_names": ["@application_msg", "@operater_msg", "@operate_flag"]}], "DS60_create_seat_id_bsr": [{"key_cnt": 18, "value_cnt": 18, "key_names": ["@application_msg", "@operater_msg", "@seat_msgs", "@id_types", "@id_nos", "@id_names", "@epay_flag", "@inven_grades", "@mobile_no", "@insure_nos", "@sequence_no", "@eticket_no", "@apply_num", "@old_seat_msgs", "@old_ext_ticket_nos", "@node_code", "@append_nos", "@input_modes"]}], "CT60_proc_double_record_log": [{"key_cnt": 7, "value_cnt": 7, "key_names": ["@application_msg", "@inner_code", "@ext_ticket_no", "@sequence_no", "@operate_flag", "@result_flag", "@out_flag"]}], "DS60_Get_Node_DB": [{"key_cnt": 8, "value_cnt": 8, "key_names": ["@train_date", "@sequence_no", "@passenger_id_no", "@ext_ticket_no", "@seat_msg", "@node", "@output", "@kind"]}, {"key_cnt": 4, "value_cnt": 4, "key_names": ["@train_date", "@passenger_id_no", "@kind", "@output"]}], "YY40_Get_yydetails": [{"key_cnt": 0, "key_names": null, "value_cnt": 9}], "CS50_Get_Brand": [{"key_cnt": 1, "value_cnt": 1, "key_names": ["@train_no"]}], "CS50_Ticket_Face": [{"key_cnt": 0, "key_names": null, "value_cnt": 39}], "CS50_Store_SR": [{"key_cnt": 0, "key_names": null, "value_cnt": 14}, {"key_cnt": 0, "key_names": null, "value_cnt": 13}], "TS60_exchange_ticket_confirm": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@application_msg", "@operater_msg", "@sequence_no", "@seat_msg", "@print_ticket_no"]}], "CS40_Special_Train": [{"key_cnt": 3, "value_cnt": 3, "key_names": ["@train_date", "@out_flag", "@train_no"]}], "BX10_proc_qualify_check": [{"key_cnt": 10, "value_cnt": 10, "key_names": ["@train_date", "@train_no", "@from_station_telecode", "@to_station_telecode", "@sale_mode", "@id_type", "@id_no", "@id_name", "@check_flag", "@flag"]}, {"key_cnt": 13, "value_cnt": 13, "key_names": ["@train_date", "@train_no", "@from_station_telecode", "@to_station_telecode", "@sale_mode", "@id_type", "@id_no", "@id_name", "@check_flag", "@flag", "@insure_label", "@ticket_status_code", "@out_flag"]}], "BX10_proc_insure_product": [{"key_cnt": 10, "value_cnt": 10, "key_names": ["@company_code", "@insure_code", "@product_code", "@range", "@train_date", "@id_type", "@id_no", "@ticket_type", "@query_flag", "@out_flag"]}, {"key_cnt": 11, "value_cnt": 11, "key_names": ["@company_code", "@insure_code", "@product_code", "@range", "@train_date", "@id_type", "@id_no", "@ticket_type", "@query_flag", "@check_flag", "@out_flag"]}], "CT40_return_win_month;1": [{"key_cnt": 5, "value_cnt": 5, "key_names": ["@start_date", "@end_date", "@inner_code", "@office_no", "@return_reason"]}], "CT50_return_win_month_fb;1": [{"key_cnt": 4, "value_cnt": 4, "key_names": ["@start_date", "@end_date", "@inner_code", "@office_no"]}], "CT40_return_train_month;1": [{"key_cnt": 4, "value_cnt": 4, "key_names": ["@start_date", "@end_date", "@inner_code", "@office_no"]}], "CS30_Fetch_Station": [{"key_cnt": 3, "value_cnt": 3, "key_names": ["@input_code", "@filter", "@flag"]}], "CS30_Fetch_Notice": [{"key_cnt": 0, "key_names": null, "value_cnt": 5}], "DQ40_left_station": [{"key_cnt": 0, "key_names": null, "value_cnt": 10}], "DZ50_get_office_no;1": [{"key_cnt": 1, "value_cnt": 1, "key_names": ["@sta_admin_code"]}], "BX10_proc_print_info_new": [{"key_cnt": 17, "value_cnt": 17, "key_names": ["@application_msg", "@operater_msg", "@sale_date", "@insure_no", "@id_type", "@id_no", "@id_name", "@reserver_id_type", "@reserver_id_no", "@reserver_name", "@insure_price", "@station_name", "@operate_type", "@adult_flag", "@insure_num", "@flag", "@out_flag"]}], "DS60_Payment_Status": [{"key_cnt": 0, "key_names": null, "value_cnt": 5}]}