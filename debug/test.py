
def read_txt(filepath):
    with open(filepath,'r') as fr:
        lines = fr.readlines()
        return [r.strip('\n') for r in lines]
    

def check_valid(data):
    if not isinstance(data, str):
        print ('1111111111')
        return False
    if len(data) != 3:
        print ('2222222222')
        return False
    if data[0] == '<' and data[-1] == '>':
        inner = data[1:-1]
        if not inner.isdigit():
            return False
        return True
    if data.isdigit():
        return True
    return False
lines = read_txt('test/1.txt')
for x in lines:
    res = check_valid(x)
